===============================================================================
                          Core文件分析报告
===============================================================================

分析时间: 2025-08-15 14:02:45
系统信息: Linux z2kmngr 3.16.0-4-686-pae #1 SMP Debian 3.16.7-ckt25-2 (2016-04-08) i686 GNU/Linux
主机名: z2kmngr
用户: zx
工作目录: /opt/z2000/nx_bin/nx_ec
可执行文件: /opt/z2000/nx_bin/nx_ec/nx_ec_service
Core文件: /opt/z2000/nx_bin/nx_ec/core
GDB版本: GNU gdb (Debian 7.7.1+dfsg-5) 7.7.1

===============================================================================
                          文件基本信息
===============================================================================

--- 可执行文件信息 ---
lrwxrwxrwx 1 zx zx 45 7月   7 15:43 /opt/z2000/nx_bin/nx_ec/nx_ec_service -> /opt/z2000/nx_bin/nx_ec/nx_ec_service-*******
/opt/z2000/nx_bin/nx_ec/nx_ec_service: symbolic link to /opt/z2000/nx_bin/nx_ec/nx_ec_service-*******

--- Core文件信息 ---
-rw------- 1 <USER> <GROUP> 240529408 8月  14 14:09 /opt/z2000/nx_bin/nx_ec/core
/opt/z2000/nx_bin/nx_ec/core: ELF 32-bit LSB core file Intel 80386, version 1 (SYSV), too many program headers (140)

===============================================================================
                          GDB分析结果
===============================================================================


warning: core file may not match specified executable file.
[New LWP 2295]
[New LWP 2296]
[New LWP 2387]
[New LWP 2388]
[New LWP 2279]
[New LWP 16633]
[New LWP 2280]
[New LWP 2389]
[New LWP 2281]
[New LWP 2283]
[New LWP 2282]
[New LWP 2292]
[New LWP 2289]
[New LWP 2275]
[New LWP 2290]
[New LWP 2293]
[New LWP 2278]
[New LWP 2294]
[New LWP 2291]
[New LWP 2285]
[Thread debugging using libthread_db enabled]
Using host libthread_db library "/lib/i386-linux-gnu/i686/cmov/libthread_db.so.1".
Core was generated by `./nx_ec_service -P'.
Program terminated with signal SIGSEGV, Segmentation fault.
#0  0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so

===============================================================================
基本信息
===============================================================================
The program being debugged is not being run.

--- 文件信息 ---
Symbols from "/opt/z2000/nx_bin/nx_ec/nx_ec_service-*******".
Local core dump file:
	`/opt/z2000/nx_bin/nx_ec/core', file type elf32-i386.
	0x08048000 - 0x08049000 is load1a
	0x08049000 - 0x08049000 is load1b
	0x08088000 - 0x08089000 is load2
	0x08299000 - 0x087c0000 is load3
	0xa5ef6000 - 0xa5ef7000 is load4
	0xa5ef7000 - 0xa66f7000 is load5
	0xa66f7000 - 0xa66f8000 is load6
	0xa66f8000 - 0xa6ef8000 is load7
	0xa6ef8000 - 0xa6ef9000 is load8
	0xa6ef9000 - 0xa76f9000 is load9
	0xa7efa000 - 0xa7efb000 is load10
	0xa7efb000 - 0xa86fb000 is load11
	0xa86fb000 - 0xa86fc000 is load12
	0xa86fc000 - 0xa8efc000 is load13
	0xa8efc000 - 0xa8efd000 is load14
	0xa8efd000 - 0xa96fd000 is load15
	0xa96fd000 - 0xa96fe000 is load16
	0xa96fe000 - 0xa9efe000 is load17
	0xa9efe000 - 0xa9eff000 is load18
	0xa9eff000 - 0xaa6ff000 is load19
	0xaa6ff000 - 0xaa700000 is load20
	0xaa700000 - 0xaaf00000 is load21
	0xaaf00000 - 0xaaf21000 is load22
	0xaaf21000 - 0xaaf21000 is load23
	0xab0ff000 - 0xab100000 is load24
	0xab100000 - 0xab900000 is load25
	0xab900000 - 0xab921000 is load26
	0xab921000 - 0xab921000 is load27
	0xabaff000 - 0xabb00000 is load28
	0xabb00000 - 0xac300000 is load29
	0xac300000 - 0xac321000 is load30
	0xac321000 - 0xac321000 is load31
	0xac400000 - 0xac421000 is load32
	0xac421000 - 0xac421000 is load33
	0xac500000 - 0xac521000 is load34
	0xac521000 - 0xac521000 is load35
	0xac6ef000 - 0xac6f0000 is load36
	0xac6f0000 - 0xacef0000 is load37
	0xae6f3000 - 0xae7fb000 is load38
	0xae7fb000 - 0xae7ff000 is load39
	0xae7ff000 - 0xae800000 is load40
	0xae800000 - 0xaf000000 is load41
	0xaf000000 - 0xaf021000 is load42
	0xaf021000 - 0xaf021000 is load43
	0xaf1ee000 - 0xaf1ef000 is load44
	0xaf1ef000 - 0xaf9ef000 is load45
	0xaf9ef000 - 0xaf9f0000 is load46
	0xaf9f0000 - 0xb01f0000 is load47
	0xb01f0000 - 0xb01f1000 is load48
	0xb01f1000 - 0xb09f1000 is load49
	0xb09f1000 - 0xb09f2000 is load50
	0xb09f2000 - 0xb11f2000 is load51
	0xb1200000 - 0xb1221000 is load52
	0xb1221000 - 0xb1221000 is load53
	0xb1313000 - 0xb1359000 is load54
	0xb1359000 - 0xb135a000 is load55
	0xb135a000 - 0xb13af000 is load56
	0xb13af000 - 0xb13b0000 is load57
	0xb13b0000 - 0xb13f1000 is load58
	0xb13f1000 - 0xb13f2000 is load59
	0xb13f2000 - 0xb13f3000 is load60
	0xb13f3000 - 0xb1bf3000 is load61
	0xb1bf3000 - 0xb1bf4000 is load62
	0xb1bf4000 - 0xb23f4000 is load63
	0xb23f4000 - 0xb23f5000 is load64
	0xb23f5000 - 0xb2bf5000 is load65
	0xb2bf5000 - 0xb2bf6000 is load66
	0xb2bf6000 - 0xb33f6000 is load67
	0xb3bf7000 - 0xb3bf8000 is load68
	0xb3bf8000 - 0xb43f8000 is load69
	0xb43f8000 - 0xb43f9000 is load70
	0xb43f9000 - 0xb4bf9000 is load71
	0xb4bf9000 - 0xb4bfa000 is load72
	0xb4bfa000 - 0xb53fa000 is load73
	0xb53fa000 - 0xb53fb000 is load74
	0xb53fb000 - 0xb5bfb000 is load75
	0xb5bfb000 - 0xb5bfc000 is load76
	0xb5bfc000 - 0xb64fd000 is load77
	0xb64fd000 - 0xb64fe000 is load78a
	0xb64fe000 - 0xb64fe000 is load78b
	0xb673a000 - 0xb6746000 is load79
	0xb6746000 - 0xb6747000 is load80a
	0xb6747000 - 0xb6747000 is load80b
	0xb675b000 - 0xb675c000 is load81
	0xb675c000 - 0xb675d000 is load82
	0xb675d000 - 0xb675d000 is load83
	0xb6772000 - 0xb6774000 is load84
	0xb6774000 - 0xb6775000 is load85a
	0xb6775000 - 0xb6775000 is load85b
	0xb67f7000 - 0xb67f9000 is load86
	0xb67f9000 - 0xb67fa000 is load87
	0xb67fa000 - 0xb6ffa000 is load88
	0xb6ffa000 - 0xb704d000 is load89
	0xb704d000 - 0xb704e000 is load90
	0xb7076000 - 0xb70ab000 is load91
	0xb70ab000 - 0xb70ac000 is load92
	0xb70ac000 - 0xb70f2000 is load93
	0xb70f2000 - 0xb70f3000 is load94
	0xb70f3000 - 0xb7127000 is load95
	0xb7127000 - 0xb7128000 is load96
	0xb7128000 - 0xb72ee000 is load97
	0xb72ee000 - 0xb7337000 is load98
	0xb7337000 - 0xb7338000 is load99
	0xb7338000 - 0xb739d000 is load100
	0xb739d000 - 0xb739f000 is load101
	0xb739f000 - 0xb73f5000 is load102
	0xb73f5000 - 0xb73f6000 is load103
	0xb73f6000 - 0xb73f9000 is load104
	0xb73f9000 - 0xb73fa000 is load105a
	0xb73fa000 - 0xb73fa000 is load105b
	0xb75a0000 - 0xb75a2000 is load106
	0xb75a2000 - 0xb75a3000 is load107
	0xb75a3000 - 0xb75a6000 is load108
	0xb75a6000 - 0xb75a7000 is load109a
	0xb75a7000 - 0xb75a7000 is load109b
	0xb75c2000 - 0xb75c3000 is load110
	0xb75c3000 - 0xb75c4000 is load111a
	0xb75c4000 - 0xb75c4000 is load111b
	0xb7607000 - 0xb7608000 is load112
	0xb7608000 - 0xb7609000 is load113
	0xb7609000 - 0xb760a000 is load114a
	0xb760a000 - 0xb760a000 is load114b
	0xb76ef000 - 0xb76f3000 is load115
	0xb76f3000 - 0xb76f4000 is load116
	0xb76f4000 - 0xb76fb000 is load117
	0xb76fb000 - 0xb76fc000 is load118a
	0xb76fc000 - 0xb76fc000 is load118b
	0xb7702000 - 0xb7703000 is load119
	0xb7703000 - 0xb7704000 is load120
	0xb7704000 - 0xb7705000 is load121
	0xb7705000 - 0xb7706000 is load122a
	0xb7706000 - 0xb7706000 is load122b
	0xb7708000 - 0xb7709000 is load123
	0xb7709000 - 0xb770a000 is load124
	0xb770a000 - 0xb770b000 is load125a
	0xb770b000 - 0xb770b000 is load125b
	0xb7722000 - 0xb7723000 is load126
	0xb7723000 - 0xb7724000 is load127
	0xb7724000 - 0xb7728000 is load128
	0xb7729000 - 0xb772a000 is load129
	0xb772a000 - 0xb772b000 is load130a
	0xb772b000 - 0xb772b000 is load130b
	0xb7735000 - 0xb7736000 is load131
	0xb7736000 - 0xb7737000 is load132
	0xb7737000 - 0xb773d000 is load133
	0xb773d000 - 0xb773e000 is load134
	0xb773e000 - 0xb7740000 is load135
	0xb7740000 - 0xb7741000 is load136a
	0xb7741000 - 0xb7741000 is load136b
	0xb775f000 - 0xb7760000 is load137
	0xb7760000 - 0xb7761000 is load138
	0xbfd7c000 - 0xbfd9e000 is load139
Local exec file:
	`/opt/z2000/nx_bin/nx_ec/nx_ec_service-*******', file type elf32-i386.
	Entry point: 0x8053260
	0x08048134 - 0x08048147 is .interp
	0x08048148 - 0x08048168 is .note.ABI-tag
	0x08048168 - 0x0804818c is .note.gnu.build-id
	0x0804818c - 0x080493cc is .hash
	0x080493cc - 0x0804a130 is .gnu.hash
	0x0804a130 - 0x0804c980 is .dynsym
	0x0804c980 - 0x08051a55 is .dynstr
	0x08051a56 - 0x08051f60 is .gnu.version
	0x08051f60 - 0x080520a0 is .gnu.version_r
	0x080520a0 - 0x080521b8 is .rel.dyn
	0x080521b8 - 0x08052730 is .rel.plt
	0x08052730 - 0x08052760 is .init
	0x08052760 - 0x08053260 is .plt
	0x08053260 - 0x0807e63c is .text
	0x0807e63c - 0x0807e658 is .fini
	0x0807e660 - 0x08080fb0 is .rodata
	0x08080fb0 - 0x08081d1c is .eh_frame_hdr
	0x08081d1c - 0x080855dc is .eh_frame
	0x080855dc - 0x08087e9e is .gcc_except_table
	0x08088000 - 0x08088054 is .ctors
	0x08088054 - 0x0808805c is .dtors
	0x0808805c - 0x08088060 is .jcr
	0x08088060 - 0x08088108 is .data.rel.ro
	0x08088108 - 0x08088208 is .dynamic
	0x08088208 - 0x08088284 is .got
	0x08088284 - 0x0808854c is .got.plt
	0x0808854c - 0x08088558 is .data
	0x08088560 - 0x080887c0 is .bss
	0xb770a154 - 0xb770a178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770a178 - 0xb770a198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770a198 - 0xb770aed4 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770aed4 - 0xb770c4c4 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770c4c4 - 0xb770d904 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770d904 - 0xb770dbc2 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770dbc4 - 0xb770ddf4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770ddf4 - 0xb770dea4 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770dea4 - 0xb770e104 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e104 - 0xb770e36c is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e36c - 0xb770e385 is .init in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e390 - 0xb770e870 is .plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e870 - 0xb771af87 is .text in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771af90 - 0xb771aff0 is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771aff0 - 0xb771b004 is .fini in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771b020 - 0xb771bd00 is .rodata in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771bd00 - 0xb771bd13 is .interp in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771bd14 - 0xb771c878 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771c878 - 0xb77207c0 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb77207c0 - 0xb7720880 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7720880 - 0xb7721838 is .hash in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722da0 - 0xb7722da8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722da8 - 0xb7722dac is .fini_array in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722dac - 0xb7722db0 is .jcr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722dc0 - 0xb7722e98 is .data.rel.ro in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722e98 - 0xb7722fb0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722fb0 - 0xb7722fe4 is .got in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723000 - 0xb7723140 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723140 - 0xb7723174 is .data in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723180 - 0xb77252ac is .bss in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7705154 - 0xb7705178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705178 - 0xb7705198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705198 - 0xb7705250 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705250 - 0xb77054f0 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77054f0 - 0xb77056fe is .dynstr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77056fe - 0xb7705752 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705754 - 0xb770581c is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770581c - 0xb770588c is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770588c - 0xb770591c is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770591c - 0xb770599c is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770599c - 0xb77059bf is .init in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77059c0 - 0xb7705ad0 is .plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705ad0 - 0xb770696c is .text in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770696c - 0xb7706980 is .fini in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706980 - 0xb7706a23 is .rodata in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706a23 - 0xb7706a36 is .interp in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706a38 - 0xb7706b0c is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706b0c - 0xb7706ff4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706ff4 - 0xb77071a8 is .hash in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708eb0 - 0xb7708eb8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708eb8 - 0xb7708ec0 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708ec0 - 0xb7708ec4 is .jcr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708ec4 - 0xb7708fcc is .dynamic in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708fcc - 0xb7709000 is .got in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7709000 - 0xb770904c is .got.plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770904c - 0xb7709050 is .data in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7709050 - 0xb7709080 is .bss in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb76fb154 - 0xb76fb178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb178 - 0xb76fb198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb198 - 0xb76fb3c0 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb3c0 - 0xb76fbb10 is .dynsym in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fbb10 - 0xb76fc0c0 is .dynstr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc0c0 - 0xb76fc1aa is .gnu.version in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc1ac - 0xb76fc274 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc274 - 0xb76fc344 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc344 - 0xb76fc3d4 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc3d4 - 0xb76fc5b4 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc5b4 - 0xb76fc5d7 is .init in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc5e0 - 0xb76fc9b0 is .plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc9b0 - 0xb76ffeaa is .text in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76ffeb0 - 0xb76fff2c is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fff2c - 0xb76fff40 is .fini in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fff40 - 0xb7700254 is .rodata in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7700254 - 0xb7700267 is .interp in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7700268 - 0xb77004ac is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb77004ac - 0xb770117c is .eh_frame in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb770117c - 0xb7701190 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7701190 - 0xb77016d0 is .hash in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702ea4 - 0xb7702ea8 is .init_array in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702ea8 - 0xb7702eac is .fini_array in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eac - 0xb7702eb0 is .jcr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eb0 - 0xb7702eb8 is __libc_subfreeres in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eb8 - 0xb7702fd0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702fd0 - 0xb7703000 is .got in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703000 - 0xb77030fc is .got.plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703100 - 0xb7703168 is .data in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703180 - 0xb7703244 is .bss in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7609134 - 0xb7609158 is .note.gnu.build-id in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7609158 - 0xb760eab8 is .gnu.hash in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb760eab8 - 0xb761d8b8 is .dynsym in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb761d8b8 - 0xb764487b is .dynstr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764487c - 0xb764663c is .gnu.version in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764663c - 0xb7646a9c is .gnu.version_d in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7646a9c - 0xb7646bbc is .gnu.version_r in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7646bbc - 0xb764bc7c is .rel.dyn in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764bc7c - 0xb764d0e4 is .rel.plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764d0e4 - 0xb764d107 is .init in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764d110 - 0xb764f9f0 is .plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764f9f0 - 0xb76bdcdd is .text in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76bdce0 - 0xb76bdcf4 is .fini in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76bdd00 - 0xb76c3868 is .rodata in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c3868 - 0xb76c3869 is .stapsdt.base in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c386c - 0xb76c8320 is .eh_frame_hdr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c8320 - 0xb76ea5fc is .eh_frame in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ea5fc - 0xb76eebc2 is .gcc_except_table in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1c8 - 0xb76ef1d8 is .tbss in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1c8 - 0xb76ef1e8 is .init_array in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1e8 - 0xb76ef1ec is .fini_array in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1ec - 0xb76ef1f0 is .jcr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef200 - 0xb76f287c is .data.rel.ro in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f287c - 0xb76f298c is .dynamic in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f298c - 0xb76f2ffc is .got in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3000 - 0xb76f3a40 is .got.plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3a40 - 0xb76f3bdc is .data in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3c00 - 0xb76fa8b0 is .bss in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb75c3154 - 0xb75c3178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c3178 - 0xb75c3198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c3198 - 0xb75c46e4 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c46e4 - 0xb75c61f4 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c61f4 - 0xb75c6efe is .dynstr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c6efe - 0xb75c7260 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7260 - 0xb75c734c is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c734c - 0xb75c73ac is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c73ac - 0xb75c7414 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7414 - 0xb75c74a4 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c74a4 - 0xb75c74c7 is .init in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c74d0 - 0xb75c7600 is .plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7600 - 0xb75f4ed5 is .text in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75f4ed8 - 0xb75f4eec is .fini in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75f4f00 - 0xb75fed34 is .rodata in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75fed34 - 0xb75fed47 is .interp in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75fed48 - 0xb75ffc9c is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75ffc9c - 0xb7605220 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7605220 - 0xb7606640 is .hash in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ebc - 0xb7607ec0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec0 - 0xb7607ec4 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec4 - 0xb7607ec8 is .jcr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec8 - 0xb7607fd8 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607fd8 - 0xb7608000 is .got in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608000 - 0xb7608054 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608054 - 0xb760805c is .data in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608060 - 0xb76080a0 is .bss in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75a60f4 - 0xb75a6118 is .note.gnu.build-id in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a6118 - 0xb75a65e0 is .gnu.hash in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a65e0 - 0xb75a6ff0 is .dynsym in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a6ff0 - 0xb75a7872 is .dynstr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7872 - 0xb75a79b4 is .gnu.version in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a79b4 - 0xb75a7b9c is .gnu.version_d in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7b9c - 0xb75a7bdc is .gnu.version_r in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7bdc - 0xb75a7c34 is .rel.dyn in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7c34 - 0xb75a7d9c is .rel.plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7d9c - 0xb75a7dbf is .init in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7dc0 - 0xb75a80a0 is .plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a80a0 - 0xb75bdf45 is .text in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bdf48 - 0xb75bdf5c is .fini in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bdf80 - 0xb75be938 is .rodata in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75be938 - 0xb75bee4c is .eh_frame_hdr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bee4c - 0xb75c19dc is .eh_frame in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29dc - 0xb75c29e4 is .init_array in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29e4 - 0xb75c29e8 is .fini_array in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29e8 - 0xb75c29ec is .jcr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29ec - 0xb75c2ae4 is .dynamic in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2ae4 - 0xb75c2b00 is .got in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2b00 - 0xb75c2bc0 is .got.plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2bc0 - 0xb75c2bd0 is .data in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2c00 - 0xb75c2d94 is .bss in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb73f9174 - 0xb73f9198 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73f9198 - 0xb73f91b8 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73f91b8 - 0xb73fcec8 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73fcec8 - 0xb7406438 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7406438 - 0xb740c15e is .dynstr in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740c15e - 0xb740d40c is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d40c - 0xb740d898 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d898 - 0xb740d8d8 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d8d8 - 0xb74102e8 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb74102e8 - 0xb7410348 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7410350 - 0xb7410420 is .plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7410420 - 0xb754091e is .text in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7540920 - 0xb75418ab is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75418b0 - 0xb7541a87 is __libc_thread_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7541aa0 - 0xb75634c4 is .rodata in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75634c4 - 0xb75634d7 is .interp in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75634d8 - 0xb756a954 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb756a954 - 0xb759bbd4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb759bbd4 - 0xb759c030 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb759c030 - 0xb759f590 is .hash in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01dc - 0xb75a01e4 is .tdata in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01e4 - 0xb75a0228 is .tbss in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01e4 - 0xb75a01f0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01f0 - 0xb75a0268 is __libc_subfreeres in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a0268 - 0xb75a026c is __libc_atexit in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a026c - 0xb75a027c is __libc_thread_subfreeres in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a0280 - 0xb75a1da8 is .data.rel.ro in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a1da8 - 0xb75a1e98 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a1e98 - 0xb75a1ff4 is .got in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2000 - 0xb75a203c is .got.plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2040 - 0xb75a2ebc is .data in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2ec0 - 0xb75a5a7c is .bss in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7740114 - 0xb7740138 is .note.gnu.build-id in /lib/ld-linux.so.2
	0xb7740138 - 0xb77401f8 is .hash in /lib/ld-linux.so.2
	0xb77401f8 - 0xb77402dc is .gnu.hash in /lib/ld-linux.so.2
	0xb77402dc - 0xb77404ac is .dynsym in /lib/ld-linux.so.2
	0xb77404ac - 0xb7740642 is .dynstr in /lib/ld-linux.so.2
	0xb7740642 - 0xb774067c is .gnu.version in /lib/ld-linux.so.2
	0xb774067c - 0xb7740744 is .gnu.version_d in /lib/ld-linux.so.2
	0xb7740744 - 0xb77407b4 is .rel.dyn in /lib/ld-linux.so.2
	0xb77407b4 - 0xb77407e4 is .rel.plt in /lib/ld-linux.so.2
	0xb77407f0 - 0xb7740860 is .plt in /lib/ld-linux.so.2
	0xb7740860 - 0xb77580fc is .text in /lib/ld-linux.so.2
	0xb7758100 - 0xb775bf80 is .rodata in /lib/ld-linux.so.2
	0xb775bf80 - 0xb775c5fc is .eh_frame_hdr in /lib/ld-linux.so.2
	0xb775c5fc - 0xb775eeb8 is .eh_frame in /lib/ld-linux.so.2
	0xb775fcc0 - 0xb775ff30 is .data.rel.ro in /lib/ld-linux.so.2
	0xb775ff30 - 0xb775ffe8 is .dynamic in /lib/ld-linux.so.2
	0xb775ffe8 - 0xb775fff4 is .got in /lib/ld-linux.so.2
	0xb7760000 - 0xb7760024 is .got.plt in /lib/ld-linux.so.2
	0xb7760040 - 0xb7760878 is .data in /lib/ld-linux.so.2
	0xb7760878 - 0xb776092c is .bss in /lib/ld-linux.so.2
	0xb739f0f4 - 0xb739f118 is .note.gnu.build-id in ./libnx_ec_model_access.so
	0xb739f118 - 0xb73a04d0 is .hash in ./libnx_ec_model_access.so
	0xb73a04d0 - 0xb73a17e8 is .gnu.hash in ./libnx_ec_model_access.so
	0xb73a17e8 - 0xb73a4618 is .dynsym in ./libnx_ec_model_access.so
	0xb73a4618 - 0xb73ac6d7 is .dynstr in ./libnx_ec_model_access.so
	0xb73ac6d8 - 0xb73acc9e is .gnu.version in ./libnx_ec_model_access.so
	0xb73acca0 - 0xb73acdc0 is .gnu.version_r in ./libnx_ec_model_access.so
	0xb73acdc0 - 0xb73ae7d0 is .rel.dyn in ./libnx_ec_model_access.so
	0xb73ae7d0 - 0xb73af610 is .rel.plt in ./libnx_ec_model_access.so
	0xb73af610 - 0xb73af640 is .init in ./libnx_ec_model_access.so
	0xb73af640 - 0xb73b12d0 is .plt in ./libnx_ec_model_access.so
	0xb73b12d0 - 0xb73e9278 is .text in ./libnx_ec_model_access.so
	0xb73e9278 - 0xb73e9294 is .fini in ./libnx_ec_model_access.so
	0xb73e92a0 - 0xb73ebb92 is .rodata in ./libnx_ec_model_access.so
	0xb73ebb94 - 0xb73ecb28 is .eh_frame_hdr in ./libnx_ec_model_access.so
	0xb73ecb28 - 0xb73f0d60 is .eh_frame in ./libnx_ec_model_access.so
	0xb73f0d60 - 0xb73f4618 is .gcc_except_table in ./libnx_ec_model_access.so
	0xb73f5000 - 0xb73f5038 is .ctors in ./libnx_ec_model_access.so
	0xb73f5038 - 0xb73f5040 is .dtors in ./libnx_ec_model_access.so
	0xb73f5040 - 0xb73f5044 is .jcr in ./libnx_ec_model_access.so
	0xb73f5060 - 0xb73f54e0 is .data.rel.ro in ./libnx_ec_model_access.so
	0xb73f54e0 - 0xb73f55d8 is .dynamic in ./libnx_ec_model_access.so
	0xb73f55d8 - 0xb73f5634 is .got in ./libnx_ec_model_access.so
	0xb73f5634 - 0xb73f5d60 is .got.plt in ./libnx_ec_model_access.so
	0xb73f5d60 - 0xb73f5d6c is .data in ./libnx_ec_model_access.so
	0xb73f5d6c - 0xb73f5da8 is .bss in ./libnx_ec_model_access.so
	0xb73380f4 - 0xb7338118 is .note.gnu.build-id in /opt/z2000/lib/libnx_dbm.so
	0xb7338118 - 0xb73395c4 is .hash in /opt/z2000/lib/libnx_dbm.so
	0xb73395c4 - 0xb733a9e4 is .gnu.hash in /opt/z2000/lib/libnx_dbm.so
	0xb733a9e4 - 0xb733dbe4 is .dynsym in /opt/z2000/lib/libnx_dbm.so
	0xb733dbe4 - 0xb7345a6f is .dynstr in /opt/z2000/lib/libnx_dbm.so
	0xb7345a70 - 0xb73460b0 is .gnu.version in /opt/z2000/lib/libnx_dbm.so
	0xb73460b0 - 0xb73461d0 is .gnu.version_r in /opt/z2000/lib/libnx_dbm.so
	0xb73461d0 - 0xb73485e8 is .rel.dyn in /opt/z2000/lib/libnx_dbm.so
	0xb73485e8 - 0xb73496b8 is .rel.plt in /opt/z2000/lib/libnx_dbm.so
	0xb73496b8 - 0xb73496e8 is .init in /opt/z2000/lib/libnx_dbm.so
	0xb73496e8 - 0xb734b898 is .plt in /opt/z2000/lib/libnx_dbm.so
	0xb734b8a0 - 0xb738b7c8 is .text in /opt/z2000/lib/libnx_dbm.so
	0xb738b7c8 - 0xb738b7e4 is .fini in /opt/z2000/lib/libnx_dbm.so
	0xb738b800 - 0xb738ea18 is .rodata in /opt/z2000/lib/libnx_dbm.so
	0xb738ea18 - 0xb738fcac is .eh_frame_hdr in /opt/z2000/lib/libnx_dbm.so
	0xb738fcac - 0xb7394dbc is .eh_frame in /opt/z2000/lib/libnx_dbm.so
	0xb7394dbc - 0xb739c508 is .gcc_except_table in /opt/z2000/lib/libnx_dbm.so
	0xb739d508 - 0xb739d54c is .ctors in /opt/z2000/lib/libnx_dbm.so
	0xb739d54c - 0xb739d554 is .dtors in /opt/z2000/lib/libnx_dbm.so
	0xb739d554 - 0xb739d558 is .jcr in /opt/z2000/lib/libnx_dbm.so
	0xb739d560 - 0xb739d788 is .data.rel.ro in /opt/z2000/lib/libnx_dbm.so
	0xb739d788 - 0xb739d880 is .dynamic in /opt/z2000/lib/libnx_dbm.so
	0xb739d880 - 0xb739d948 is .got in /opt/z2000/lib/libnx_dbm.so
	0xb739d948 - 0xb739e1bc is .got.plt in /opt/z2000/lib/libnx_dbm.so
	0xb739e1c0 - 0xb739eb60 is .data in /opt/z2000/lib/libnx_dbm.so
	0xb739eb60 - 0xb739ec24 is .bss in /opt/z2000/lib/libnx_dbm.so
	0xb71280f4 - 0xb7128118 is .note.gnu.build-id in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7128118 - 0xb712d85c is .hash in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb712d85c - 0xb7133bd4 is .gnu.hash in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7133bd4 - 0xb7141874 is .dynsym in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7141874 - 0xb715ce44 is .dynstr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715ce44 - 0xb715e9d8 is .gnu.version in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715e9d8 - 0xb715eb68 is .gnu.version_r in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715eb68 - 0xb7165a98 is .rel.dyn in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7165a98 - 0xb71687a8 is .rel.plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb71687a8 - 0xb71687d8 is .init in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb71687d8 - 0xb716e208 is .plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb716e210 - 0xb720a3a8 is .text in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb720a3a8 - 0xb720a3c4 is .fini in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb720a3e0 - 0xb72ea200 is .rodata in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ea200 - 0xb72ea93c is .eh_frame_hdr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ea93c - 0xb72ec794 is .eh_frame in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ec794 - 0xb72edd03 is .gcc_except_table in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee000 - 0xb72ee03c is .ctors in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee03c - 0xb72ee044 is .dtors in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee044 - 0xb72ee048 is .jcr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee060 - 0xb72f00d8 is .data.rel.ro in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f00d8 - 0xb72f01d0 is .dynamic in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f01d0 - 0xb72f05e8 is .got in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f05e8 - 0xb72f1c7c is .got.plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f1c80 - 0xb73366a0 is .data in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb73366a0 - 0xb7337484 is .bss in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb772a154 - 0xb772a178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a178 - 0xb772a198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a198 - 0xb772a4a8 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a4a8 - 0xb772abe8 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772abe8 - 0xb772b4c4 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b4c4 - 0xb772b5ac is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b5ac - 0xb772b5e4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b5e4 - 0xb772b654 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b654 - 0xb772b6bc is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b6bc - 0xb772b804 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b804 - 0xb772b827 is .init in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b830 - 0xb772bad0 is .plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772bad0 - 0xb77328ab is .text in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77328ac - 0xb77328c0 is .fini in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77328c0 - 0xb7732a81 is .rodata in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732a81 - 0xb7732a94 is .interp in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732a94 - 0xb7732d88 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732d88 - 0xb77342f4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77342f4 - 0xb7734830 is .hash in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ec4 - 0xb7735ec8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ec8 - 0xb7735ecc is .fini_array in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ecc - 0xb7735ed0 is .jcr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ed0 - 0xb7735fd8 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735fd8 - 0xb7736000 is .got in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7736000 - 0xb77360b0 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77360c0 - 0xb7736104 is .data in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7736120 - 0xb77363d4 is .bss in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb70f30f4 - 0xb70f3118 is .note.gnu.build-id in ./libnx_ec_srv_mediator.so
	0xb70f3118 - 0xb70f3c68 is .hash in ./libnx_ec_srv_mediator.so
	0xb70f3c68 - 0xb70f4618 is .gnu.hash in ./libnx_ec_srv_mediator.so
	0xb70f4618 - 0xb70f62c8 is .dynsym in ./libnx_ec_srv_mediator.so
	0xb70f62c8 - 0xb70f9e59 is .dynstr in ./libnx_ec_srv_mediator.so
	0xb70f9e5a - 0xb70fa1f0 is .gnu.version in ./libnx_ec_srv_mediator.so
	0xb70fa1f0 - 0xb70fa310 is .gnu.version_r in ./libnx_ec_srv_mediator.so
	0xb70fa310 - 0xb70fc530 is .rel.dyn in ./libnx_ec_srv_mediator.so
	0xb70fc530 - 0xb70fce68 is .rel.plt in ./libnx_ec_srv_mediator.so
	0xb70fce68 - 0xb70fce98 is .init in ./libnx_ec_srv_mediator.so
	0xb70fce98 - 0xb70fe118 is .plt in ./libnx_ec_srv_mediator.so
	0xb70fe120 - 0xb711fa58 is .text in ./libnx_ec_srv_mediator.so
	0xb711fa58 - 0xb711fa74 is .fini in ./libnx_ec_srv_mediator.so
	0xb711fa80 - 0xb712126e is .rodata in ./libnx_ec_srv_mediator.so
	0xb7121270 - 0xb7121b74 is .eh_frame_hdr in ./libnx_ec_srv_mediator.so
	0xb7121b74 - 0xb71241e8 is .eh_frame in ./libnx_ec_srv_mediator.so
	0xb71241e8 - 0xb7126390 is .gcc_except_table in ./libnx_ec_srv_mediator.so
	0xb7127390 - 0xb71273c4 is .ctors in ./libnx_ec_srv_mediator.so
	0xb71273c4 - 0xb71273cc is .dtors in ./libnx_ec_srv_mediator.so
	0xb71273cc - 0xb71273d0 is .jcr in ./libnx_ec_srv_mediator.so
	0xb71273e0 - 0xb7127488 is .data.rel.ro in ./libnx_ec_srv_mediator.so
	0xb7127488 - 0xb7127580 is .dynamic in ./libnx_ec_srv_mediator.so
	0xb7127580 - 0xb71275d0 is .got in ./libnx_ec_srv_mediator.so
	0xb71275d0 - 0xb7127a78 is .got.plt in ./libnx_ec_srv_mediator.so
	0xb7127a78 - 0xb7127a80 is .data in ./libnx_ec_srv_mediator.so
	0xb7127a80 - 0xb7127ab8 is .bss in ./libnx_ec_srv_mediator.so
	0xb70ac0f4 - 0xb70ac118 is .note.gnu.build-id in ./libnx_ec_bus_swap.so
	0xb70ac118 - 0xb70ad2d4 is .hash in ./libnx_ec_bus_swap.so
	0xb70ad2d4 - 0xb70adff0 is .gnu.hash in ./libnx_ec_bus_swap.so
	0xb70adff0 - 0xb70b0630 is .dynsym in ./libnx_ec_bus_swap.so
	0xb70b0630 - 0xb70b578a is .dynstr in ./libnx_ec_bus_swap.so
	0xb70b578a - 0xb70b5c52 is .gnu.version in ./libnx_ec_bus_swap.so
	0xb70b5c54 - 0xb70b5d74 is .gnu.version_r in ./libnx_ec_bus_swap.so
	0xb70b5d74 - 0xb70b987c is .rel.dyn in ./libnx_ec_bus_swap.so
	0xb70b987c - 0xb70ba2c4 is .rel.plt in ./libnx_ec_bus_swap.so
	0xb70ba2c4 - 0xb70ba2f4 is .init in ./libnx_ec_bus_swap.so
	0xb70ba2f4 - 0xb70bb794 is .plt in ./libnx_ec_bus_swap.so
	0xb70bb7a0 - 0xb70e77d8 is .text in ./libnx_ec_bus_swap.so
	0xb70e77d8 - 0xb70e77f4 is .fini in ./libnx_ec_bus_swap.so
	0xb70e7800 - 0xb70ea423 is .rodata in ./libnx_ec_bus_swap.so
	0xb70ea424 - 0xb70eb1b8 is .eh_frame_hdr in ./libnx_ec_bus_swap.so
	0xb70eb1b8 - 0xb70eeaa4 is .eh_frame in ./libnx_ec_bus_swap.so
	0xb70eeaa4 - 0xb70f12c4 is .gcc_except_table in ./libnx_ec_bus_swap.so
	0xb70f22c4 - 0xb70f2314 is .ctors in ./libnx_ec_bus_swap.so
	0xb70f2314 - 0xb70f231c is .dtors in ./libnx_ec_bus_swap.so
	0xb70f231c - 0xb70f2320 is .jcr in ./libnx_ec_bus_swap.so
	0xb70f2320 - 0xb70f23e0 is .data.rel.ro in ./libnx_ec_bus_swap.so
	0xb70f23e0 - 0xb70f24d8 is .dynamic in ./libnx_ec_bus_swap.so
	0xb70f24d8 - 0xb70f2548 is .got in ./libnx_ec_bus_swap.so
	0xb70f2548 - 0xb70f2a78 is .got.plt in ./libnx_ec_bus_swap.so
	0xb70f2a78 - 0xb70f2a80 is .data in ./libnx_ec_bus_swap.so
	0xb70f2a80 - 0xb70f2ad4 is .bss in ./libnx_ec_bus_swap.so
	0xb70760f4 - 0xb7076118 is .note.gnu.build-id in ./libnx_ec_net_listen.so
	0xb7076118 - 0xb7076cc4 is .hash in ./libnx_ec_net_listen.so
	0xb7076cc4 - 0xb70776d4 is .gnu.hash in ./libnx_ec_net_listen.so
	0xb70776d4 - 0xb70794f4 is .dynsym in ./libnx_ec_net_listen.so
	0xb70794f4 - 0xb707d2a9 is .dynstr in ./libnx_ec_net_listen.so
	0xb707d2aa - 0xb707d66e is .gnu.version in ./libnx_ec_net_listen.so
	0xb707d670 - 0xb707d790 is .gnu.version_r in ./libnx_ec_net_listen.so
	0xb707d790 - 0xb7080138 is .rel.dyn in ./libnx_ec_net_listen.so
	0xb7080138 - 0xb7080aa8 is .rel.plt in ./libnx_ec_net_listen.so
	0xb7080aa8 - 0xb7080ad8 is .init in ./libnx_ec_net_listen.so
	0xb7080ad8 - 0xb7081dc8 is .plt in ./libnx_ec_net_listen.so
	0xb7081dd0 - 0xb70a2f88 is .text in ./libnx_ec_net_listen.so
	0xb70a2f88 - 0xb70a2fa4 is .fini in ./libnx_ec_net_listen.so
	0xb70a2fc0 - 0xb70a4f3e is .rodata in ./libnx_ec_net_listen.so
	0xb70a4f40 - 0xb70a58dc is .eh_frame_hdr in ./libnx_ec_net_listen.so
	0xb70a58dc - 0xb70a8194 is .eh_frame in ./libnx_ec_net_listen.so
	0xb70a8194 - 0xb70aa2a4 is .gcc_except_table in ./libnx_ec_net_listen.so
	0xb70ab2a4 - 0xb70ab2e0 is .ctors in ./libnx_ec_net_listen.so
	0xb70ab2e0 - 0xb70ab2e8 is .dtors in ./libnx_ec_net_listen.so
	0xb70ab2e8 - 0xb70ab2ec is .jcr in ./libnx_ec_net_listen.so
	0xb70ab300 - 0xb70ab3a8 is .data.rel.ro in ./libnx_ec_net_listen.so
	0xb70ab3a8 - 0xb70ab4a0 is .dynamic in ./libnx_ec_net_listen.so
	0xb70ab4a0 - 0xb70ab4f8 is .got in ./libnx_ec_net_listen.so
	0xb70ab4f8 - 0xb70ab9bc is .got.plt in ./libnx_ec_net_listen.so
	0xb70ab9bc - 0xb70ab9c8 is .data in ./libnx_ec_net_listen.so
	0xb70ab9c8 - 0xb70aba10 is .bss in ./libnx_ec_net_listen.so
	0xb6ffa0f4 - 0xb6ffa118 is .note.gnu.build-id in ./libnx_ec_node_mgr.so
	0xb6ffa118 - 0xb6ffb43c is .hash in ./libnx_ec_node_mgr.so
	0xb6ffb43c - 0xb6ffc6b4 is .gnu.hash in ./libnx_ec_node_mgr.so
	0xb6ffc6b4 - 0xb6fff294 is .dynsym in ./libnx_ec_node_mgr.so
	0xb6fff294 - 0xb7005841 is .dynstr in ./libnx_ec_node_mgr.so
	0xb7005842 - 0xb7005dbe is .gnu.version in ./libnx_ec_node_mgr.so
	0xb7005dc0 - 0xb7005ee0 is .gnu.version_r in ./libnx_ec_node_mgr.so
	0xb7005ee0 - 0xb7009a50 is .rel.dyn in ./libnx_ec_node_mgr.so
	0xb7009a50 - 0xb700a778 is .rel.plt in ./libnx_ec_node_mgr.so
	0xb700a778 - 0xb700a7a8 is .init in ./libnx_ec_node_mgr.so
	0xb700a7a8 - 0xb700c208 is .plt in ./libnx_ec_node_mgr.so
	0xb700c210 - 0xb70403b8 is .text in ./libnx_ec_node_mgr.so
	0xb70403b8 - 0xb70403d4 is .fini in ./libnx_ec_node_mgr.so
	0xb70403e0 - 0xb7043df7 is .rodata in ./libnx_ec_node_mgr.so
	0xb7043df8 - 0xb7044df4 is .eh_frame_hdr in ./libnx_ec_node_mgr.so
	0xb7044df4 - 0xb70491b4 is .eh_frame in ./libnx_ec_node_mgr.so
	0xb70491b4 - 0xb704c29c is .gcc_except_table in ./libnx_ec_node_mgr.so
	0xb704d29c - 0xb704d2f4 is .ctors in ./libnx_ec_node_mgr.so
	0xb704d2f4 - 0xb704d2fc is .dtors in ./libnx_ec_node_mgr.so
	0xb704d2fc - 0xb704d300 is .jcr in ./libnx_ec_node_mgr.so
	0xb704d300 - 0xb704d488 is .data.rel.ro in ./libnx_ec_node_mgr.so
	0xb704d488 - 0xb704d580 is .dynamic in ./libnx_ec_node_mgr.so
	0xb704d580 - 0xb704d5f8 is .got in ./libnx_ec_node_mgr.so
	0xb704d5f8 - 0xb704dc98 is .got.plt in ./libnx_ec_node_mgr.so
	0xb704dc98 - 0xb704dca0 is .data in ./libnx_ec_node_mgr.so
	0xb704dca0 - 0xb704dd0c is .bss in ./libnx_ec_node_mgr.so
	0xb67740f4 - 0xb6774118 is .note.gnu.build-id in /opt/z2000/lib/libnx_mb.so
	0xb6774118 - 0xb67765a0 is .hash in /opt/z2000/lib/libnx_mb.so
	0xb67765a0 - 0xb677816c is .gnu.hash in /opt/z2000/lib/libnx_mb.so
	0xb677816c - 0xb677d2fc is .dynsym in /opt/z2000/lib/libnx_mb.so
	0xb677d2fc - 0xb6789b89 is .dynstr in /opt/z2000/lib/libnx_mb.so
	0xb6789b8a - 0xb678a5bc is .gnu.version in /opt/z2000/lib/libnx_mb.so
	0xb678a5bc - 0xb678a70c is .gnu.version_r in /opt/z2000/lib/libnx_mb.so
	0xb678a70c - 0xb678bb44 is .rel.dyn in /opt/z2000/lib/libnx_mb.so
	0xb678bb44 - 0xb678d3ac is .rel.plt in /opt/z2000/lib/libnx_mb.so
	0xb678d3ac - 0xb678d3dc is .init in /opt/z2000/lib/libnx_mb.so
	0xb678d3dc - 0xb67904bc is .plt in /opt/z2000/lib/libnx_mb.so
	0xb67904c0 - 0xb67e4478 is .text in /opt/z2000/lib/libnx_mb.so
	0xb67e4478 - 0xb67e4494 is .fini in /opt/z2000/lib/libnx_mb.so
	0xb67e44a0 - 0xb67e7e8c is .rodata in /opt/z2000/lib/libnx_mb.so
	0xb67e7e8c - 0xb67e99b8 is .eh_frame_hdr in /opt/z2000/lib/libnx_mb.so
	0xb67e99b8 - 0xb67f0b68 is .eh_frame in /opt/z2000/lib/libnx_mb.so
	0xb67f0b68 - 0xb67f6bcb is .gcc_except_table in /opt/z2000/lib/libnx_mb.so
	0xb67f7000 - 0xb67f708c is .ctors in /opt/z2000/lib/libnx_mb.so
	0xb67f708c - 0xb67f7094 is .dtors in /opt/z2000/lib/libnx_mb.so
	0xb67f7094 - 0xb67f7098 is .jcr in /opt/z2000/lib/libnx_mb.so
	0xb67f70a0 - 0xb67f7d90 is .data.rel.ro in /opt/z2000/lib/libnx_mb.so
	0xb67f7d90 - 0xb67f7e98 is .dynamic in /opt/z2000/lib/libnx_mb.so
	0xb67f7e98 - 0xb67f7ffc is .got in /opt/z2000/lib/libnx_mb.so
	0xb67f7ffc - 0xb67f8c3c is .got.plt in /opt/z2000/lib/libnx_mb.so
	0xb67f8c3c - 0xb67f8c60 is .data in /opt/z2000/lib/libnx_mb.so
	0xb67f8c60 - 0xb67f8dd0 is .bss in /opt/z2000/lib/libnx_mb.so
	0xb6746154 - 0xb6746178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746178 - 0xb6746198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746198 - 0xb6746828 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746828 - 0xb6747788 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6747788 - 0xb6748414 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748414 - 0xb6748600 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748600 - 0xb67486a4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67486a4 - 0xb6748724 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748724 - 0xb67487bc is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67487bc - 0xb6748b04 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748b04 - 0xb6748b27 is .init in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748b30 - 0xb67491d0 is .plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67491d0 - 0xb6755723 is .text in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6755724 - 0xb6755738 is .fini in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6755740 - 0xb6757540 is .rodata in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757540 - 0xb6757553 is .interp in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757554 - 0xb6757b00 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757b00 - 0xb675a3c0 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675a3c0 - 0xb675aeec is .hash in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beac - 0xb675beb0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb0 - 0xb675beb4 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb4 - 0xb675beb8 is .jcr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb8 - 0xb675bfc0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675bfc0 - 0xb675c000 is .got in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c000 - 0xb675c1b0 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c1b0 - 0xb675c1b4 is .data in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c1c0 - 0xb675e7e8 is .bss in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb64fd0f4 - 0xb64fd118 is .note.gnu.build-id in /opt/z2000/lib/libIceE.so.13
	0xb64fd118 - 0xb6508998 is .hash in /opt/z2000/lib/libIceE.so.13
	0xb6508998 - 0xb6515f08 is .gnu.hash in /opt/z2000/lib/libIceE.so.13
	0xb6515f08 - 0xb65340b8 is .dynsym in /opt/z2000/lib/libIceE.so.13
	0xb65340b8 - 0xb65c2a87 is .dynstr in /opt/z2000/lib/libIceE.so.13
	0xb65c2a88 - 0xb65c66be is .gnu.version in /opt/z2000/lib/libIceE.so.13
	0xb65c66c0 - 0xb65c67d0 is .gnu.version_r in /opt/z2000/lib/libIceE.so.13
	0xb65c67d0 - 0xb65ce040 is .rel.dyn in /opt/z2000/lib/libIceE.so.13
	0xb65ce040 - 0xb65d96e0 is .rel.plt in /opt/z2000/lib/libIceE.so.13
	0xb65d96e0 - 0xb65d9710 is .init in /opt/z2000/lib/libIceE.so.13
	0xb65d9710 - 0xb65f0460 is .plt in /opt/z2000/lib/libIceE.so.13
	0xb65f0460 - 0xb66d4b48 is .text in /opt/z2000/lib/libIceE.so.13
	0xb66d4b48 - 0xb66d4b64 is .fini in /opt/z2000/lib/libIceE.so.13
	0xb66d4b80 - 0xb66e4ce0 is .rodata in /opt/z2000/lib/libIceE.so.13
	0xb66e4ce0 - 0xb66f236c is .eh_frame_hdr in /opt/z2000/lib/libIceE.so.13
	0xb66f236c - 0xb67258d4 is .eh_frame in /opt/z2000/lib/libIceE.so.13
	0xb67258d4 - 0xb67397a1 is .gcc_except_table in /opt/z2000/lib/libIceE.so.13
	0xb673a000 - 0xb673a048 is .ctors in /opt/z2000/lib/libIceE.so.13
	0xb673a048 - 0xb673a050 is .dtors in /opt/z2000/lib/libIceE.so.13
	0xb673a050 - 0xb673a054 is .jcr in /opt/z2000/lib/libIceE.so.13
	0xb673a060 - 0xb673f7cc is .data.rel.ro in /opt/z2000/lib/libIceE.so.13
	0xb673f7cc - 0xb673f8d4 is .dynamic in /opt/z2000/lib/libIceE.so.13
	0xb673f8d4 - 0xb673fe78 is .got in /opt/z2000/lib/libIceE.so.13
	0xb673fe78 - 0xb67459d4 is .got.plt in /opt/z2000/lib/libIceE.so.13
	0xb67459d4 - 0xb6745b3c is .data in /opt/z2000/lib/libIceE.so.13
	0xb6745b40 - 0xb6745d24 is .bss in /opt/z2000/lib/libIceE.so.13
	0xae6f30f4 - 0xae6f3118 is .note.gnu.build-id in ./libnx_ec_pro_srv_gw104.so
	0xae6f3118 - 0xae6f5d18 is .gnu.hash in ./libnx_ec_pro_srv_gw104.so
	0xae6f5d18 - 0xae6fc878 is .dynsym in ./libnx_ec_pro_srv_gw104.so
	0xae6fc878 - 0xae7100c4 is .dynstr in ./libnx_ec_pro_srv_gw104.so
	0xae7100c4 - 0xae710e30 is .gnu.version in ./libnx_ec_pro_srv_gw104.so
	0xae710e30 - 0xae710f90 is .gnu.version_r in ./libnx_ec_pro_srv_gw104.so
	0xae710f90 - 0xae71a8c0 is .rel.dyn in ./libnx_ec_pro_srv_gw104.so
	0xae71a8c0 - 0xae71bb80 is .rel.plt in ./libnx_ec_pro_srv_gw104.so
	0xae71bb80 - 0xae71bba3 is .init in ./libnx_ec_pro_srv_gw104.so
	0xae71bbb0 - 0xae71e140 is .plt in ./libnx_ec_pro_srv_gw104.so
	0xae71e140 - 0xae7cffb4 is .text in ./libnx_ec_pro_srv_gw104.so
	0xae7cffb4 - 0xae7cffc8 is .fini in ./libnx_ec_pro_srv_gw104.so
	0xae7d0000 - 0xae7dbfe4 is .rodata in ./libnx_ec_pro_srv_gw104.so
	0xae7dbfe4 - 0xae7de8d0 is .eh_frame_hdr in ./libnx_ec_pro_srv_gw104.so
	0xae7de8d0 - 0xae7f2410 is .eh_frame in ./libnx_ec_pro_srv_gw104.so
	0xae7f2410 - 0xae7fa1c4 is .gcc_except_table in ./libnx_ec_pro_srv_gw104.so
	0xae7fb1c4 - 0xae7fb2b4 is .init_array in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2b4 - 0xae7fb2b8 is .fini_array in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2b8 - 0xae7fb2bc is .jcr in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2c0 - 0xae7fdca8 is .data.rel.ro in ./libnx_ec_pro_srv_gw104.so
	0xae7fdca8 - 0xae7fddb8 is .dynamic in ./libnx_ec_pro_srv_gw104.so
	0xae7fddb8 - 0xae7fdec4 is .got in ./libnx_ec_pro_srv_gw104.so
	0xae7fdec4 - 0xae7fe830 is .got.plt in ./libnx_ec_pro_srv_gw104.so
	0xae7fe840 - 0xae7fe8e4 is .data in ./libnx_ec_pro_srv_gw104.so
	0xae7fe900 - 0xae7fed98 is .bss in ./libnx_ec_pro_srv_gw104.so
	0xb13b00f4 - 0xb13b0118 is .note.gnu.build-id in ./libnx_ec_pro_commuflow_104.so
	0xb13b0118 - 0xb13b129c is .hash in ./libnx_ec_pro_commuflow_104.so
	0xb13b129c - 0xb13b1f7c is .gnu.hash in ./libnx_ec_pro_commuflow_104.so
	0xb13b1f7c - 0xb13b44dc is .dynsym in ./libnx_ec_pro_commuflow_104.so
	0xb13b44dc - 0xb13b94da is .dynstr in ./libnx_ec_pro_commuflow_104.so
	0xb13b94da - 0xb13b9986 is .gnu.version in ./libnx_ec_pro_commuflow_104.so
	0xb13b9988 - 0xb13b9aa8 is .gnu.version_r in ./libnx_ec_pro_commuflow_104.so
	0xb13b9aa8 - 0xb13bc860 is .rel.dyn in ./libnx_ec_pro_commuflow_104.so
	0xb13bc860 - 0xb13bd210 is .rel.plt in ./libnx_ec_pro_commuflow_104.so
	0xb13bd210 - 0xb13bd240 is .init in ./libnx_ec_pro_commuflow_104.so
	0xb13bd240 - 0xb13be5b0 is .plt in ./libnx_ec_pro_commuflow_104.so
	0xb13be5b0 - 0xb13e6d88 is .text in ./libnx_ec_pro_commuflow_104.so
	0xb13e6d88 - 0xb13e6da4 is .fini in ./libnx_ec_pro_commuflow_104.so
	0xb13e6dc0 - 0xb13e98a3 is .rodata in ./libnx_ec_pro_commuflow_104.so
	0xb13e98a4 - 0xb13ea570 is .eh_frame_hdr in ./libnx_ec_pro_commuflow_104.so
	0xb13ea570 - 0xb13edb54 is .eh_frame in ./libnx_ec_pro_commuflow_104.so
	0xb13edb54 - 0xb13f0078 is .gcc_except_table in ./libnx_ec_pro_commuflow_104.so
	0xb13f1078 - 0xb13f10c4 is .ctors in ./libnx_ec_pro_commuflow_104.so
	0xb13f10c4 - 0xb13f10cc is .dtors in ./libnx_ec_pro_commuflow_104.so
	0xb13f10cc - 0xb13f10d0 is .jcr in ./libnx_ec_pro_commuflow_104.so
	0xb13f10e0 - 0xb13f1590 is .data.rel.ro in ./libnx_ec_pro_commuflow_104.so
	0xb13f1590 - 0xb13f1688 is .dynamic in ./libnx_ec_pro_commuflow_104.so
	0xb13f1688 - 0xb13f16f4 is .got in ./libnx_ec_pro_commuflow_104.so
	0xb13f16f4 - 0xb13f1bd8 is .got.plt in ./libnx_ec_pro_commuflow_104.so
	0xb13f1bd8 - 0xb13f1be4 is .data in ./libnx_ec_pro_commuflow_104.so
	0xb13f1be4 - 0xb13f1c30 is .bss in ./libnx_ec_pro_commuflow_104.so
	0xb135a0f4 - 0xb135a118 is .note.gnu.build-id in ./libnx_ec_msg_operation.so
	0xb135a118 - 0xb135b478 is .hash in ./libnx_ec_msg_operation.so
	0xb135b478 - 0xb135c72c is .gnu.hash in ./libnx_ec_msg_operation.so
	0xb135c72c - 0xb135f3fc is .dynsym in ./libnx_ec_msg_operation.so
	0xb135f3fc - 0xb1365b73 is .dynstr in ./libnx_ec_msg_operation.so
	0xb1365b74 - 0xb136610e is .gnu.version in ./libnx_ec_msg_operation.so
	0xb1366110 - 0xb1366230 is .gnu.version_r in ./libnx_ec_msg_operation.so
	0xb1366230 - 0xb1369a38 is .rel.dyn in ./libnx_ec_msg_operation.so
	0xb1369a38 - 0xb136a638 is .rel.plt in ./libnx_ec_msg_operation.so
	0xb136a638 - 0xb136a668 is .init in ./libnx_ec_msg_operation.so
	0xb136a668 - 0xb136be78 is .plt in ./libnx_ec_msg_operation.so
	0xb136be80 - 0xb13a2fc8 is .text in ./libnx_ec_msg_operation.so
	0xb13a2fc8 - 0xb13a2fe4 is .fini in ./libnx_ec_msg_operation.so
	0xb13a3000 - 0xb13a608f is .rodata in ./libnx_ec_msg_operation.so
	0xb13a6090 - 0xb13a7104 is .eh_frame_hdr in ./libnx_ec_msg_operation.so
	0xb13a7104 - 0xb13ab5c4 is .eh_frame in ./libnx_ec_msg_operation.so
	0xb13ab5c4 - 0xb13ae458 is .gcc_except_table in ./libnx_ec_msg_operation.so
	0xb13af458 - 0xb13af4b8 is .ctors in ./libnx_ec_msg_operation.so
	0xb13af4b8 - 0xb13af4c0 is .dtors in ./libnx_ec_msg_operation.so
	0xb13af4c0 - 0xb13af4c4 is .jcr in ./libnx_ec_msg_operation.so
	0xb13af4e0 - 0xb13af810 is .data.rel.ro in ./libnx_ec_msg_operation.so
	0xb13af810 - 0xb13af908 is .dynamic in ./libnx_ec_msg_operation.so
	0xb13af908 - 0xb13af970 is .got in ./libnx_ec_msg_operation.so
	0xb13af970 - 0xb13aff7c is .got.plt in ./libnx_ec_msg_operation.so
	0xb13aff7c - 0xb13aff84 is .data in ./libnx_ec_msg_operation.so
	0xb13aff84 - 0xb13affe4 is .bss in ./libnx_ec_msg_operation.so
	0xb13130f4 - 0xb1313118 is .note.gnu.build-id in ./libnx_ec_pro_operation.so
	0xb1313118 - 0xb13141f0 is .hash in ./libnx_ec_pro_operation.so
	0xb13141f0 - 0xb1314d10 is .gnu.hash in ./libnx_ec_pro_operation.so
	0xb1314d10 - 0xb1316fc0 is .dynsym in ./libnx_ec_pro_operation.so
	0xb1316fc0 - 0xb131bea8 is .dynstr in ./libnx_ec_pro_operation.so
	0xb131bea8 - 0xb131c2fe is .gnu.version in ./libnx_ec_pro_operation.so
	0xb131c300 - 0xb131c420 is .gnu.version_r in ./libnx_ec_pro_operation.so
	0xb131c420 - 0xb131e830 is .rel.dyn in ./libnx_ec_pro_operation.so
	0xb131e830 - 0xb131f2c8 is .rel.plt in ./libnx_ec_pro_operation.so
	0xb131f2c8 - 0xb131f2f8 is .init in ./libnx_ec_pro_operation.so
	0xb131f2f8 - 0xb1320838 is .plt in ./libnx_ec_pro_operation.so
	0xb1320840 - 0xb134f8e8 is .text in ./libnx_ec_pro_operation.so
	0xb134f8e8 - 0xb134f904 is .fini in ./libnx_ec_pro_operation.so
	0xb134f920 - 0xb13522ee is .rodata in ./libnx_ec_pro_operation.so
	0xb13522f0 - 0xb1352e9c is .eh_frame_hdr in ./libnx_ec_pro_operation.so
	0xb1352e9c - 0xb13560a4 is .eh_frame in ./libnx_ec_pro_operation.so
	0xb13560a4 - 0xb1358ba4 is .gcc_except_table in ./libnx_ec_pro_operation.so
	0xb1359000 - 0xb1359040 is .ctors in ./libnx_ec_pro_operation.so
	0xb1359040 - 0xb1359048 is .dtors in ./libnx_ec_pro_operation.so
	0xb1359048 - 0xb135904c is .jcr in ./libnx_ec_pro_operation.so
	0xb1359060 - 0xb1359308 is .data.rel.ro in ./libnx_ec_pro_operation.so
	0xb1359308 - 0xb1359400 is .dynamic in ./libnx_ec_pro_operation.so
	0xb1359400 - 0xb1359474 is .got in ./libnx_ec_pro_operation.so
	0xb1359474 - 0xb13599cc is .got.plt in ./libnx_ec_pro_operation.so
	0xb13599cc - 0xb13599d4 is .data in ./libnx_ec_pro_operation.so
	0xb13599d4 - 0xb1359a14 is .bss in ./libnx_ec_pro_operation.so

--- 目标信息 ---
Symbols from "/opt/z2000/nx_bin/nx_ec/nx_ec_service-*******".
Local core dump file:
	`/opt/z2000/nx_bin/nx_ec/core', file type elf32-i386.
	0x08048000 - 0x08049000 is load1a
	0x08049000 - 0x08049000 is load1b
	0x08088000 - 0x08089000 is load2
	0x08299000 - 0x087c0000 is load3
	0xa5ef6000 - 0xa5ef7000 is load4
	0xa5ef7000 - 0xa66f7000 is load5
	0xa66f7000 - 0xa66f8000 is load6
	0xa66f8000 - 0xa6ef8000 is load7
	0xa6ef8000 - 0xa6ef9000 is load8
	0xa6ef9000 - 0xa76f9000 is load9
	0xa7efa000 - 0xa7efb000 is load10
	0xa7efb000 - 0xa86fb000 is load11
	0xa86fb000 - 0xa86fc000 is load12
	0xa86fc000 - 0xa8efc000 is load13
	0xa8efc000 - 0xa8efd000 is load14
	0xa8efd000 - 0xa96fd000 is load15
	0xa96fd000 - 0xa96fe000 is load16
	0xa96fe000 - 0xa9efe000 is load17
	0xa9efe000 - 0xa9eff000 is load18
	0xa9eff000 - 0xaa6ff000 is load19
	0xaa6ff000 - 0xaa700000 is load20
	0xaa700000 - 0xaaf00000 is load21
	0xaaf00000 - 0xaaf21000 is load22
	0xaaf21000 - 0xaaf21000 is load23
	0xab0ff000 - 0xab100000 is load24
	0xab100000 - 0xab900000 is load25
	0xab900000 - 0xab921000 is load26
	0xab921000 - 0xab921000 is load27
	0xabaff000 - 0xabb00000 is load28
	0xabb00000 - 0xac300000 is load29
	0xac300000 - 0xac321000 is load30
	0xac321000 - 0xac321000 is load31
	0xac400000 - 0xac421000 is load32
	0xac421000 - 0xac421000 is load33
	0xac500000 - 0xac521000 is load34
	0xac521000 - 0xac521000 is load35
	0xac6ef000 - 0xac6f0000 is load36
	0xac6f0000 - 0xacef0000 is load37
	0xae6f3000 - 0xae7fb000 is load38
	0xae7fb000 - 0xae7ff000 is load39
	0xae7ff000 - 0xae800000 is load40
	0xae800000 - 0xaf000000 is load41
	0xaf000000 - 0xaf021000 is load42
	0xaf021000 - 0xaf021000 is load43
	0xaf1ee000 - 0xaf1ef000 is load44
	0xaf1ef000 - 0xaf9ef000 is load45
	0xaf9ef000 - 0xaf9f0000 is load46
	0xaf9f0000 - 0xb01f0000 is load47
	0xb01f0000 - 0xb01f1000 is load48
	0xb01f1000 - 0xb09f1000 is load49
	0xb09f1000 - 0xb09f2000 is load50
	0xb09f2000 - 0xb11f2000 is load51
	0xb1200000 - 0xb1221000 is load52
	0xb1221000 - 0xb1221000 is load53
	0xb1313000 - 0xb1359000 is load54
	0xb1359000 - 0xb135a000 is load55
	0xb135a000 - 0xb13af000 is load56
	0xb13af000 - 0xb13b0000 is load57
	0xb13b0000 - 0xb13f1000 is load58
	0xb13f1000 - 0xb13f2000 is load59
	0xb13f2000 - 0xb13f3000 is load60
	0xb13f3000 - 0xb1bf3000 is load61
	0xb1bf3000 - 0xb1bf4000 is load62
	0xb1bf4000 - 0xb23f4000 is load63
	0xb23f4000 - 0xb23f5000 is load64
	0xb23f5000 - 0xb2bf5000 is load65
	0xb2bf5000 - 0xb2bf6000 is load66
	0xb2bf6000 - 0xb33f6000 is load67
	0xb3bf7000 - 0xb3bf8000 is load68
	0xb3bf8000 - 0xb43f8000 is load69
	0xb43f8000 - 0xb43f9000 is load70
	0xb43f9000 - 0xb4bf9000 is load71
	0xb4bf9000 - 0xb4bfa000 is load72
	0xb4bfa000 - 0xb53fa000 is load73
	0xb53fa000 - 0xb53fb000 is load74
	0xb53fb000 - 0xb5bfb000 is load75
	0xb5bfb000 - 0xb5bfc000 is load76
	0xb5bfc000 - 0xb64fd000 is load77
	0xb64fd000 - 0xb64fe000 is load78a
	0xb64fe000 - 0xb64fe000 is load78b
	0xb673a000 - 0xb6746000 is load79
	0xb6746000 - 0xb6747000 is load80a
	0xb6747000 - 0xb6747000 is load80b
	0xb675b000 - 0xb675c000 is load81
	0xb675c000 - 0xb675d000 is load82
	0xb675d000 - 0xb675d000 is load83
	0xb6772000 - 0xb6774000 is load84
	0xb6774000 - 0xb6775000 is load85a
	0xb6775000 - 0xb6775000 is load85b
	0xb67f7000 - 0xb67f9000 is load86
	0xb67f9000 - 0xb67fa000 is load87
	0xb67fa000 - 0xb6ffa000 is load88
	0xb6ffa000 - 0xb704d000 is load89
	0xb704d000 - 0xb704e000 is load90
	0xb7076000 - 0xb70ab000 is load91
	0xb70ab000 - 0xb70ac000 is load92
	0xb70ac000 - 0xb70f2000 is load93
	0xb70f2000 - 0xb70f3000 is load94
	0xb70f3000 - 0xb7127000 is load95
	0xb7127000 - 0xb7128000 is load96
	0xb7128000 - 0xb72ee000 is load97
	0xb72ee000 - 0xb7337000 is load98
	0xb7337000 - 0xb7338000 is load99
	0xb7338000 - 0xb739d000 is load100
	0xb739d000 - 0xb739f000 is load101
	0xb739f000 - 0xb73f5000 is load102
	0xb73f5000 - 0xb73f6000 is load103
	0xb73f6000 - 0xb73f9000 is load104
	0xb73f9000 - 0xb73fa000 is load105a
	0xb73fa000 - 0xb73fa000 is load105b
	0xb75a0000 - 0xb75a2000 is load106
	0xb75a2000 - 0xb75a3000 is load107
	0xb75a3000 - 0xb75a6000 is load108
	0xb75a6000 - 0xb75a7000 is load109a
	0xb75a7000 - 0xb75a7000 is load109b
	0xb75c2000 - 0xb75c3000 is load110
	0xb75c3000 - 0xb75c4000 is load111a
	0xb75c4000 - 0xb75c4000 is load111b
	0xb7607000 - 0xb7608000 is load112
	0xb7608000 - 0xb7609000 is load113
	0xb7609000 - 0xb760a000 is load114a
	0xb760a000 - 0xb760a000 is load114b
	0xb76ef000 - 0xb76f3000 is load115
	0xb76f3000 - 0xb76f4000 is load116
	0xb76f4000 - 0xb76fb000 is load117
	0xb76fb000 - 0xb76fc000 is load118a
	0xb76fc000 - 0xb76fc000 is load118b
	0xb7702000 - 0xb7703000 is load119
	0xb7703000 - 0xb7704000 is load120
	0xb7704000 - 0xb7705000 is load121
	0xb7705000 - 0xb7706000 is load122a
	0xb7706000 - 0xb7706000 is load122b
	0xb7708000 - 0xb7709000 is load123
	0xb7709000 - 0xb770a000 is load124
	0xb770a000 - 0xb770b000 is load125a
	0xb770b000 - 0xb770b000 is load125b
	0xb7722000 - 0xb7723000 is load126
	0xb7723000 - 0xb7724000 is load127
	0xb7724000 - 0xb7728000 is load128
	0xb7729000 - 0xb772a000 is load129
	0xb772a000 - 0xb772b000 is load130a
	0xb772b000 - 0xb772b000 is load130b
	0xb7735000 - 0xb7736000 is load131
	0xb7736000 - 0xb7737000 is load132
	0xb7737000 - 0xb773d000 is load133
	0xb773d000 - 0xb773e000 is load134
	0xb773e000 - 0xb7740000 is load135
	0xb7740000 - 0xb7741000 is load136a
	0xb7741000 - 0xb7741000 is load136b
	0xb775f000 - 0xb7760000 is load137
	0xb7760000 - 0xb7761000 is load138
	0xbfd7c000 - 0xbfd9e000 is load139
Local exec file:
	`/opt/z2000/nx_bin/nx_ec/nx_ec_service-*******', file type elf32-i386.
	Entry point: 0x8053260
	0x08048134 - 0x08048147 is .interp
	0x08048148 - 0x08048168 is .note.ABI-tag
	0x08048168 - 0x0804818c is .note.gnu.build-id
	0x0804818c - 0x080493cc is .hash
	0x080493cc - 0x0804a130 is .gnu.hash
	0x0804a130 - 0x0804c980 is .dynsym
	0x0804c980 - 0x08051a55 is .dynstr
	0x08051a56 - 0x08051f60 is .gnu.version
	0x08051f60 - 0x080520a0 is .gnu.version_r
	0x080520a0 - 0x080521b8 is .rel.dyn
	0x080521b8 - 0x08052730 is .rel.plt
	0x08052730 - 0x08052760 is .init
	0x08052760 - 0x08053260 is .plt
	0x08053260 - 0x0807e63c is .text
	0x0807e63c - 0x0807e658 is .fini
	0x0807e660 - 0x08080fb0 is .rodata
	0x08080fb0 - 0x08081d1c is .eh_frame_hdr
	0x08081d1c - 0x080855dc is .eh_frame
	0x080855dc - 0x08087e9e is .gcc_except_table
	0x08088000 - 0x08088054 is .ctors
	0x08088054 - 0x0808805c is .dtors
	0x0808805c - 0x08088060 is .jcr
	0x08088060 - 0x08088108 is .data.rel.ro
	0x08088108 - 0x08088208 is .dynamic
	0x08088208 - 0x08088284 is .got
	0x08088284 - 0x0808854c is .got.plt
	0x0808854c - 0x08088558 is .data
	0x08088560 - 0x080887c0 is .bss
	0xb770a154 - 0xb770a178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770a178 - 0xb770a198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770a198 - 0xb770aed4 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770aed4 - 0xb770c4c4 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770c4c4 - 0xb770d904 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770d904 - 0xb770dbc2 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770dbc4 - 0xb770ddf4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770ddf4 - 0xb770dea4 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770dea4 - 0xb770e104 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e104 - 0xb770e36c is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e36c - 0xb770e385 is .init in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e390 - 0xb770e870 is .plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb770e870 - 0xb771af87 is .text in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771af90 - 0xb771aff0 is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771aff0 - 0xb771b004 is .fini in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771b020 - 0xb771bd00 is .rodata in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771bd00 - 0xb771bd13 is .interp in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771bd14 - 0xb771c878 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb771c878 - 0xb77207c0 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb77207c0 - 0xb7720880 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7720880 - 0xb7721838 is .hash in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722da0 - 0xb7722da8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722da8 - 0xb7722dac is .fini_array in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722dac - 0xb7722db0 is .jcr in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722dc0 - 0xb7722e98 is .data.rel.ro in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722e98 - 0xb7722fb0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7722fb0 - 0xb7722fe4 is .got in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723000 - 0xb7723140 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723140 - 0xb7723174 is .data in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7723180 - 0xb77252ac is .bss in /lib/i386-linux-gnu/i686/cmov/libpthread.so.0
	0xb7705154 - 0xb7705178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705178 - 0xb7705198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705198 - 0xb7705250 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705250 - 0xb77054f0 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77054f0 - 0xb77056fe is .dynstr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77056fe - 0xb7705752 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705754 - 0xb770581c is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770581c - 0xb770588c is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770588c - 0xb770591c is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770591c - 0xb770599c is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770599c - 0xb77059bf is .init in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb77059c0 - 0xb7705ad0 is .plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7705ad0 - 0xb770696c is .text in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770696c - 0xb7706980 is .fini in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706980 - 0xb7706a23 is .rodata in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706a23 - 0xb7706a36 is .interp in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706a38 - 0xb7706b0c is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706b0c - 0xb7706ff4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7706ff4 - 0xb77071a8 is .hash in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708eb0 - 0xb7708eb8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708eb8 - 0xb7708ec0 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708ec0 - 0xb7708ec4 is .jcr in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708ec4 - 0xb7708fcc is .dynamic in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7708fcc - 0xb7709000 is .got in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7709000 - 0xb770904c is .got.plt in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb770904c - 0xb7709050 is .data in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb7709050 - 0xb7709080 is .bss in /lib/i386-linux-gnu/i686/cmov/libdl.so.2
	0xb76fb154 - 0xb76fb178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb178 - 0xb76fb198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb198 - 0xb76fb3c0 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fb3c0 - 0xb76fbb10 is .dynsym in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fbb10 - 0xb76fc0c0 is .dynstr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc0c0 - 0xb76fc1aa is .gnu.version in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc1ac - 0xb76fc274 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc274 - 0xb76fc344 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc344 - 0xb76fc3d4 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc3d4 - 0xb76fc5b4 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc5b4 - 0xb76fc5d7 is .init in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc5e0 - 0xb76fc9b0 is .plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fc9b0 - 0xb76ffeaa is .text in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76ffeb0 - 0xb76fff2c is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fff2c - 0xb76fff40 is .fini in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb76fff40 - 0xb7700254 is .rodata in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7700254 - 0xb7700267 is .interp in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7700268 - 0xb77004ac is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb77004ac - 0xb770117c is .eh_frame in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb770117c - 0xb7701190 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7701190 - 0xb77016d0 is .hash in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702ea4 - 0xb7702ea8 is .init_array in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702ea8 - 0xb7702eac is .fini_array in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eac - 0xb7702eb0 is .jcr in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eb0 - 0xb7702eb8 is __libc_subfreeres in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702eb8 - 0xb7702fd0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7702fd0 - 0xb7703000 is .got in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703000 - 0xb77030fc is .got.plt in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703100 - 0xb7703168 is .data in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7703180 - 0xb7703244 is .bss in /lib/i386-linux-gnu/i686/cmov/librt.so.1
	0xb7609134 - 0xb7609158 is .note.gnu.build-id in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7609158 - 0xb760eab8 is .gnu.hash in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb760eab8 - 0xb761d8b8 is .dynsym in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb761d8b8 - 0xb764487b is .dynstr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764487c - 0xb764663c is .gnu.version in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764663c - 0xb7646a9c is .gnu.version_d in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7646a9c - 0xb7646bbc is .gnu.version_r in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb7646bbc - 0xb764bc7c is .rel.dyn in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764bc7c - 0xb764d0e4 is .rel.plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764d0e4 - 0xb764d107 is .init in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764d110 - 0xb764f9f0 is .plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb764f9f0 - 0xb76bdcdd is .text in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76bdce0 - 0xb76bdcf4 is .fini in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76bdd00 - 0xb76c3868 is .rodata in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c3868 - 0xb76c3869 is .stapsdt.base in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c386c - 0xb76c8320 is .eh_frame_hdr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76c8320 - 0xb76ea5fc is .eh_frame in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ea5fc - 0xb76eebc2 is .gcc_except_table in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1c8 - 0xb76ef1d8 is .tbss in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1c8 - 0xb76ef1e8 is .init_array in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1e8 - 0xb76ef1ec is .fini_array in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef1ec - 0xb76ef1f0 is .jcr in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76ef200 - 0xb76f287c is .data.rel.ro in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f287c - 0xb76f298c is .dynamic in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f298c - 0xb76f2ffc is .got in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3000 - 0xb76f3a40 is .got.plt in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3a40 - 0xb76f3bdc is .data in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb76f3c00 - 0xb76fa8b0 is .bss in /usr/lib/i386-linux-gnu/libstdc++.so.6
	0xb75c3154 - 0xb75c3178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c3178 - 0xb75c3198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c3198 - 0xb75c46e4 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c46e4 - 0xb75c61f4 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c61f4 - 0xb75c6efe is .dynstr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c6efe - 0xb75c7260 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7260 - 0xb75c734c is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c734c - 0xb75c73ac is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c73ac - 0xb75c7414 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7414 - 0xb75c74a4 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c74a4 - 0xb75c74c7 is .init in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c74d0 - 0xb75c7600 is .plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75c7600 - 0xb75f4ed5 is .text in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75f4ed8 - 0xb75f4eec is .fini in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75f4f00 - 0xb75fed34 is .rodata in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75fed34 - 0xb75fed47 is .interp in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75fed48 - 0xb75ffc9c is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75ffc9c - 0xb7605220 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7605220 - 0xb7606640 is .hash in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ebc - 0xb7607ec0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec0 - 0xb7607ec4 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec4 - 0xb7607ec8 is .jcr in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607ec8 - 0xb7607fd8 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7607fd8 - 0xb7608000 is .got in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608000 - 0xb7608054 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608054 - 0xb760805c is .data in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb7608060 - 0xb76080a0 is .bss in /lib/i386-linux-gnu/i686/cmov/libm.so.6
	0xb75a60f4 - 0xb75a6118 is .note.gnu.build-id in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a6118 - 0xb75a65e0 is .gnu.hash in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a65e0 - 0xb75a6ff0 is .dynsym in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a6ff0 - 0xb75a7872 is .dynstr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7872 - 0xb75a79b4 is .gnu.version in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a79b4 - 0xb75a7b9c is .gnu.version_d in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7b9c - 0xb75a7bdc is .gnu.version_r in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7bdc - 0xb75a7c34 is .rel.dyn in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7c34 - 0xb75a7d9c is .rel.plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7d9c - 0xb75a7dbf is .init in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a7dc0 - 0xb75a80a0 is .plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75a80a0 - 0xb75bdf45 is .text in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bdf48 - 0xb75bdf5c is .fini in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bdf80 - 0xb75be938 is .rodata in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75be938 - 0xb75bee4c is .eh_frame_hdr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75bee4c - 0xb75c19dc is .eh_frame in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29dc - 0xb75c29e4 is .init_array in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29e4 - 0xb75c29e8 is .fini_array in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29e8 - 0xb75c29ec is .jcr in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c29ec - 0xb75c2ae4 is .dynamic in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2ae4 - 0xb75c2b00 is .got in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2b00 - 0xb75c2bc0 is .got.plt in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2bc0 - 0xb75c2bd0 is .data in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2c00 - 0xb75c2d94 is .bss in /lib/i386-linux-gnu/libgcc_s.so.1
	0xb73f9174 - 0xb73f9198 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73f9198 - 0xb73f91b8 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73f91b8 - 0xb73fcec8 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb73fcec8 - 0xb7406438 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7406438 - 0xb740c15e is .dynstr in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740c15e - 0xb740d40c is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d40c - 0xb740d898 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d898 - 0xb740d8d8 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb740d8d8 - 0xb74102e8 is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb74102e8 - 0xb7410348 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7410350 - 0xb7410420 is .plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7410420 - 0xb754091e is .text in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7540920 - 0xb75418ab is __libc_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75418b0 - 0xb7541a87 is __libc_thread_freeres_fn in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7541aa0 - 0xb75634c4 is .rodata in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75634c4 - 0xb75634d7 is .interp in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75634d8 - 0xb756a954 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb756a954 - 0xb759bbd4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb759bbd4 - 0xb759c030 is .gcc_except_table in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb759c030 - 0xb759f590 is .hash in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01dc - 0xb75a01e4 is .tdata in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01e4 - 0xb75a0228 is .tbss in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01e4 - 0xb75a01f0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a01f0 - 0xb75a0268 is __libc_subfreeres in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a0268 - 0xb75a026c is __libc_atexit in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a026c - 0xb75a027c is __libc_thread_subfreeres in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a0280 - 0xb75a1da8 is .data.rel.ro in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a1da8 - 0xb75a1e98 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a1e98 - 0xb75a1ff4 is .got in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2000 - 0xb75a203c is .got.plt in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2040 - 0xb75a2ebc is .data in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb75a2ec0 - 0xb75a5a7c is .bss in /lib/i386-linux-gnu/i686/cmov/libc.so.6
	0xb7740114 - 0xb7740138 is .note.gnu.build-id in /lib/ld-linux.so.2
	0xb7740138 - 0xb77401f8 is .hash in /lib/ld-linux.so.2
	0xb77401f8 - 0xb77402dc is .gnu.hash in /lib/ld-linux.so.2
	0xb77402dc - 0xb77404ac is .dynsym in /lib/ld-linux.so.2
	0xb77404ac - 0xb7740642 is .dynstr in /lib/ld-linux.so.2
	0xb7740642 - 0xb774067c is .gnu.version in /lib/ld-linux.so.2
	0xb774067c - 0xb7740744 is .gnu.version_d in /lib/ld-linux.so.2
	0xb7740744 - 0xb77407b4 is .rel.dyn in /lib/ld-linux.so.2
	0xb77407b4 - 0xb77407e4 is .rel.plt in /lib/ld-linux.so.2
	0xb77407f0 - 0xb7740860 is .plt in /lib/ld-linux.so.2
	0xb7740860 - 0xb77580fc is .text in /lib/ld-linux.so.2
	0xb7758100 - 0xb775bf80 is .rodata in /lib/ld-linux.so.2
	0xb775bf80 - 0xb775c5fc is .eh_frame_hdr in /lib/ld-linux.so.2
	0xb775c5fc - 0xb775eeb8 is .eh_frame in /lib/ld-linux.so.2
	0xb775fcc0 - 0xb775ff30 is .data.rel.ro in /lib/ld-linux.so.2
	0xb775ff30 - 0xb775ffe8 is .dynamic in /lib/ld-linux.so.2
	0xb775ffe8 - 0xb775fff4 is .got in /lib/ld-linux.so.2
	0xb7760000 - 0xb7760024 is .got.plt in /lib/ld-linux.so.2
	0xb7760040 - 0xb7760878 is .data in /lib/ld-linux.so.2
	0xb7760878 - 0xb776092c is .bss in /lib/ld-linux.so.2
	0xb739f0f4 - 0xb739f118 is .note.gnu.build-id in ./libnx_ec_model_access.so
	0xb739f118 - 0xb73a04d0 is .hash in ./libnx_ec_model_access.so
	0xb73a04d0 - 0xb73a17e8 is .gnu.hash in ./libnx_ec_model_access.so
	0xb73a17e8 - 0xb73a4618 is .dynsym in ./libnx_ec_model_access.so
	0xb73a4618 - 0xb73ac6d7 is .dynstr in ./libnx_ec_model_access.so
	0xb73ac6d8 - 0xb73acc9e is .gnu.version in ./libnx_ec_model_access.so
	0xb73acca0 - 0xb73acdc0 is .gnu.version_r in ./libnx_ec_model_access.so
	0xb73acdc0 - 0xb73ae7d0 is .rel.dyn in ./libnx_ec_model_access.so
	0xb73ae7d0 - 0xb73af610 is .rel.plt in ./libnx_ec_model_access.so
	0xb73af610 - 0xb73af640 is .init in ./libnx_ec_model_access.so
	0xb73af640 - 0xb73b12d0 is .plt in ./libnx_ec_model_access.so
	0xb73b12d0 - 0xb73e9278 is .text in ./libnx_ec_model_access.so
	0xb73e9278 - 0xb73e9294 is .fini in ./libnx_ec_model_access.so
	0xb73e92a0 - 0xb73ebb92 is .rodata in ./libnx_ec_model_access.so
	0xb73ebb94 - 0xb73ecb28 is .eh_frame_hdr in ./libnx_ec_model_access.so
	0xb73ecb28 - 0xb73f0d60 is .eh_frame in ./libnx_ec_model_access.so
	0xb73f0d60 - 0xb73f4618 is .gcc_except_table in ./libnx_ec_model_access.so
	0xb73f5000 - 0xb73f5038 is .ctors in ./libnx_ec_model_access.so
	0xb73f5038 - 0xb73f5040 is .dtors in ./libnx_ec_model_access.so
	0xb73f5040 - 0xb73f5044 is .jcr in ./libnx_ec_model_access.so
	0xb73f5060 - 0xb73f54e0 is .data.rel.ro in ./libnx_ec_model_access.so
	0xb73f54e0 - 0xb73f55d8 is .dynamic in ./libnx_ec_model_access.so
	0xb73f55d8 - 0xb73f5634 is .got in ./libnx_ec_model_access.so
	0xb73f5634 - 0xb73f5d60 is .got.plt in ./libnx_ec_model_access.so
	0xb73f5d60 - 0xb73f5d6c is .data in ./libnx_ec_model_access.so
	0xb73f5d6c - 0xb73f5da8 is .bss in ./libnx_ec_model_access.so
	0xb73380f4 - 0xb7338118 is .note.gnu.build-id in /opt/z2000/lib/libnx_dbm.so
	0xb7338118 - 0xb73395c4 is .hash in /opt/z2000/lib/libnx_dbm.so
	0xb73395c4 - 0xb733a9e4 is .gnu.hash in /opt/z2000/lib/libnx_dbm.so
	0xb733a9e4 - 0xb733dbe4 is .dynsym in /opt/z2000/lib/libnx_dbm.so
	0xb733dbe4 - 0xb7345a6f is .dynstr in /opt/z2000/lib/libnx_dbm.so
	0xb7345a70 - 0xb73460b0 is .gnu.version in /opt/z2000/lib/libnx_dbm.so
	0xb73460b0 - 0xb73461d0 is .gnu.version_r in /opt/z2000/lib/libnx_dbm.so
	0xb73461d0 - 0xb73485e8 is .rel.dyn in /opt/z2000/lib/libnx_dbm.so
	0xb73485e8 - 0xb73496b8 is .rel.plt in /opt/z2000/lib/libnx_dbm.so
	0xb73496b8 - 0xb73496e8 is .init in /opt/z2000/lib/libnx_dbm.so
	0xb73496e8 - 0xb734b898 is .plt in /opt/z2000/lib/libnx_dbm.so
	0xb734b8a0 - 0xb738b7c8 is .text in /opt/z2000/lib/libnx_dbm.so
	0xb738b7c8 - 0xb738b7e4 is .fini in /opt/z2000/lib/libnx_dbm.so
	0xb738b800 - 0xb738ea18 is .rodata in /opt/z2000/lib/libnx_dbm.so
	0xb738ea18 - 0xb738fcac is .eh_frame_hdr in /opt/z2000/lib/libnx_dbm.so
	0xb738fcac - 0xb7394dbc is .eh_frame in /opt/z2000/lib/libnx_dbm.so
	0xb7394dbc - 0xb739c508 is .gcc_except_table in /opt/z2000/lib/libnx_dbm.so
	0xb739d508 - 0xb739d54c is .ctors in /opt/z2000/lib/libnx_dbm.so
	0xb739d54c - 0xb739d554 is .dtors in /opt/z2000/lib/libnx_dbm.so
	0xb739d554 - 0xb739d558 is .jcr in /opt/z2000/lib/libnx_dbm.so
	0xb739d560 - 0xb739d788 is .data.rel.ro in /opt/z2000/lib/libnx_dbm.so
	0xb739d788 - 0xb739d880 is .dynamic in /opt/z2000/lib/libnx_dbm.so
	0xb739d880 - 0xb739d948 is .got in /opt/z2000/lib/libnx_dbm.so
	0xb739d948 - 0xb739e1bc is .got.plt in /opt/z2000/lib/libnx_dbm.so
	0xb739e1c0 - 0xb739eb60 is .data in /opt/z2000/lib/libnx_dbm.so
	0xb739eb60 - 0xb739ec24 is .bss in /opt/z2000/lib/libnx_dbm.so
	0xb71280f4 - 0xb7128118 is .note.gnu.build-id in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7128118 - 0xb712d85c is .hash in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb712d85c - 0xb7133bd4 is .gnu.hash in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7133bd4 - 0xb7141874 is .dynsym in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7141874 - 0xb715ce44 is .dynstr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715ce44 - 0xb715e9d8 is .gnu.version in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715e9d8 - 0xb715eb68 is .gnu.version_r in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb715eb68 - 0xb7165a98 is .rel.dyn in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb7165a98 - 0xb71687a8 is .rel.plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb71687a8 - 0xb71687d8 is .init in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb71687d8 - 0xb716e208 is .plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb716e210 - 0xb720a3a8 is .text in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb720a3a8 - 0xb720a3c4 is .fini in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb720a3e0 - 0xb72ea200 is .rodata in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ea200 - 0xb72ea93c is .eh_frame_hdr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ea93c - 0xb72ec794 is .eh_frame in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ec794 - 0xb72edd03 is .gcc_except_table in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee000 - 0xb72ee03c is .ctors in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee03c - 0xb72ee044 is .dtors in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee044 - 0xb72ee048 is .jcr in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72ee060 - 0xb72f00d8 is .data.rel.ro in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f00d8 - 0xb72f01d0 is .dynamic in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f01d0 - 0xb72f05e8 is .got in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f05e8 - 0xb72f1c7c is .got.plt in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb72f1c80 - 0xb73366a0 is .data in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb73366a0 - 0xb7337484 is .bss in /opt/z2000/lib/libplm_dbm_mysql.so
	0xb772a154 - 0xb772a178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a178 - 0xb772a198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a198 - 0xb772a4a8 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772a4a8 - 0xb772abe8 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772abe8 - 0xb772b4c4 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b4c4 - 0xb772b5ac is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b5ac - 0xb772b5e4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b5e4 - 0xb772b654 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b654 - 0xb772b6bc is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b6bc - 0xb772b804 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b804 - 0xb772b827 is .init in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772b830 - 0xb772bad0 is .plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb772bad0 - 0xb77328ab is .text in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77328ac - 0xb77328c0 is .fini in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77328c0 - 0xb7732a81 is .rodata in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732a81 - 0xb7732a94 is .interp in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732a94 - 0xb7732d88 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7732d88 - 0xb77342f4 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77342f4 - 0xb7734830 is .hash in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ec4 - 0xb7735ec8 is .init_array in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ec8 - 0xb7735ecc is .fini_array in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ecc - 0xb7735ed0 is .jcr in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735ed0 - 0xb7735fd8 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7735fd8 - 0xb7736000 is .got in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7736000 - 0xb77360b0 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb77360c0 - 0xb7736104 is .data in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb7736120 - 0xb77363d4 is .bss in /lib/i386-linux-gnu/i686/cmov/libnss_files.so.2
	0xb70f30f4 - 0xb70f3118 is .note.gnu.build-id in ./libnx_ec_srv_mediator.so
	0xb70f3118 - 0xb70f3c68 is .hash in ./libnx_ec_srv_mediator.so
	0xb70f3c68 - 0xb70f4618 is .gnu.hash in ./libnx_ec_srv_mediator.so
	0xb70f4618 - 0xb70f62c8 is .dynsym in ./libnx_ec_srv_mediator.so
	0xb70f62c8 - 0xb70f9e59 is .dynstr in ./libnx_ec_srv_mediator.so
	0xb70f9e5a - 0xb70fa1f0 is .gnu.version in ./libnx_ec_srv_mediator.so
	0xb70fa1f0 - 0xb70fa310 is .gnu.version_r in ./libnx_ec_srv_mediator.so
	0xb70fa310 - 0xb70fc530 is .rel.dyn in ./libnx_ec_srv_mediator.so
	0xb70fc530 - 0xb70fce68 is .rel.plt in ./libnx_ec_srv_mediator.so
	0xb70fce68 - 0xb70fce98 is .init in ./libnx_ec_srv_mediator.so
	0xb70fce98 - 0xb70fe118 is .plt in ./libnx_ec_srv_mediator.so
	0xb70fe120 - 0xb711fa58 is .text in ./libnx_ec_srv_mediator.so
	0xb711fa58 - 0xb711fa74 is .fini in ./libnx_ec_srv_mediator.so
	0xb711fa80 - 0xb712126e is .rodata in ./libnx_ec_srv_mediator.so
	0xb7121270 - 0xb7121b74 is .eh_frame_hdr in ./libnx_ec_srv_mediator.so
	0xb7121b74 - 0xb71241e8 is .eh_frame in ./libnx_ec_srv_mediator.so
	0xb71241e8 - 0xb7126390 is .gcc_except_table in ./libnx_ec_srv_mediator.so
	0xb7127390 - 0xb71273c4 is .ctors in ./libnx_ec_srv_mediator.so
	0xb71273c4 - 0xb71273cc is .dtors in ./libnx_ec_srv_mediator.so
	0xb71273cc - 0xb71273d0 is .jcr in ./libnx_ec_srv_mediator.so
	0xb71273e0 - 0xb7127488 is .data.rel.ro in ./libnx_ec_srv_mediator.so
	0xb7127488 - 0xb7127580 is .dynamic in ./libnx_ec_srv_mediator.so
	0xb7127580 - 0xb71275d0 is .got in ./libnx_ec_srv_mediator.so
	0xb71275d0 - 0xb7127a78 is .got.plt in ./libnx_ec_srv_mediator.so
	0xb7127a78 - 0xb7127a80 is .data in ./libnx_ec_srv_mediator.so
	0xb7127a80 - 0xb7127ab8 is .bss in ./libnx_ec_srv_mediator.so
	0xb70ac0f4 - 0xb70ac118 is .note.gnu.build-id in ./libnx_ec_bus_swap.so
	0xb70ac118 - 0xb70ad2d4 is .hash in ./libnx_ec_bus_swap.so
	0xb70ad2d4 - 0xb70adff0 is .gnu.hash in ./libnx_ec_bus_swap.so
	0xb70adff0 - 0xb70b0630 is .dynsym in ./libnx_ec_bus_swap.so
	0xb70b0630 - 0xb70b578a is .dynstr in ./libnx_ec_bus_swap.so
	0xb70b578a - 0xb70b5c52 is .gnu.version in ./libnx_ec_bus_swap.so
	0xb70b5c54 - 0xb70b5d74 is .gnu.version_r in ./libnx_ec_bus_swap.so
	0xb70b5d74 - 0xb70b987c is .rel.dyn in ./libnx_ec_bus_swap.so
	0xb70b987c - 0xb70ba2c4 is .rel.plt in ./libnx_ec_bus_swap.so
	0xb70ba2c4 - 0xb70ba2f4 is .init in ./libnx_ec_bus_swap.so
	0xb70ba2f4 - 0xb70bb794 is .plt in ./libnx_ec_bus_swap.so
	0xb70bb7a0 - 0xb70e77d8 is .text in ./libnx_ec_bus_swap.so
	0xb70e77d8 - 0xb70e77f4 is .fini in ./libnx_ec_bus_swap.so
	0xb70e7800 - 0xb70ea423 is .rodata in ./libnx_ec_bus_swap.so
	0xb70ea424 - 0xb70eb1b8 is .eh_frame_hdr in ./libnx_ec_bus_swap.so
	0xb70eb1b8 - 0xb70eeaa4 is .eh_frame in ./libnx_ec_bus_swap.so
	0xb70eeaa4 - 0xb70f12c4 is .gcc_except_table in ./libnx_ec_bus_swap.so
	0xb70f22c4 - 0xb70f2314 is .ctors in ./libnx_ec_bus_swap.so
	0xb70f2314 - 0xb70f231c is .dtors in ./libnx_ec_bus_swap.so
	0xb70f231c - 0xb70f2320 is .jcr in ./libnx_ec_bus_swap.so
	0xb70f2320 - 0xb70f23e0 is .data.rel.ro in ./libnx_ec_bus_swap.so
	0xb70f23e0 - 0xb70f24d8 is .dynamic in ./libnx_ec_bus_swap.so
	0xb70f24d8 - 0xb70f2548 is .got in ./libnx_ec_bus_swap.so
	0xb70f2548 - 0xb70f2a78 is .got.plt in ./libnx_ec_bus_swap.so
	0xb70f2a78 - 0xb70f2a80 is .data in ./libnx_ec_bus_swap.so
	0xb70f2a80 - 0xb70f2ad4 is .bss in ./libnx_ec_bus_swap.so
	0xb70760f4 - 0xb7076118 is .note.gnu.build-id in ./libnx_ec_net_listen.so
	0xb7076118 - 0xb7076cc4 is .hash in ./libnx_ec_net_listen.so
	0xb7076cc4 - 0xb70776d4 is .gnu.hash in ./libnx_ec_net_listen.so
	0xb70776d4 - 0xb70794f4 is .dynsym in ./libnx_ec_net_listen.so
	0xb70794f4 - 0xb707d2a9 is .dynstr in ./libnx_ec_net_listen.so
	0xb707d2aa - 0xb707d66e is .gnu.version in ./libnx_ec_net_listen.so
	0xb707d670 - 0xb707d790 is .gnu.version_r in ./libnx_ec_net_listen.so
	0xb707d790 - 0xb7080138 is .rel.dyn in ./libnx_ec_net_listen.so
	0xb7080138 - 0xb7080aa8 is .rel.plt in ./libnx_ec_net_listen.so
	0xb7080aa8 - 0xb7080ad8 is .init in ./libnx_ec_net_listen.so
	0xb7080ad8 - 0xb7081dc8 is .plt in ./libnx_ec_net_listen.so
	0xb7081dd0 - 0xb70a2f88 is .text in ./libnx_ec_net_listen.so
	0xb70a2f88 - 0xb70a2fa4 is .fini in ./libnx_ec_net_listen.so
	0xb70a2fc0 - 0xb70a4f3e is .rodata in ./libnx_ec_net_listen.so
	0xb70a4f40 - 0xb70a58dc is .eh_frame_hdr in ./libnx_ec_net_listen.so
	0xb70a58dc - 0xb70a8194 is .eh_frame in ./libnx_ec_net_listen.so
	0xb70a8194 - 0xb70aa2a4 is .gcc_except_table in ./libnx_ec_net_listen.so
	0xb70ab2a4 - 0xb70ab2e0 is .ctors in ./libnx_ec_net_listen.so
	0xb70ab2e0 - 0xb70ab2e8 is .dtors in ./libnx_ec_net_listen.so
	0xb70ab2e8 - 0xb70ab2ec is .jcr in ./libnx_ec_net_listen.so
	0xb70ab300 - 0xb70ab3a8 is .data.rel.ro in ./libnx_ec_net_listen.so
	0xb70ab3a8 - 0xb70ab4a0 is .dynamic in ./libnx_ec_net_listen.so
	0xb70ab4a0 - 0xb70ab4f8 is .got in ./libnx_ec_net_listen.so
	0xb70ab4f8 - 0xb70ab9bc is .got.plt in ./libnx_ec_net_listen.so
	0xb70ab9bc - 0xb70ab9c8 is .data in ./libnx_ec_net_listen.so
	0xb70ab9c8 - 0xb70aba10 is .bss in ./libnx_ec_net_listen.so
	0xb6ffa0f4 - 0xb6ffa118 is .note.gnu.build-id in ./libnx_ec_node_mgr.so
	0xb6ffa118 - 0xb6ffb43c is .hash in ./libnx_ec_node_mgr.so
	0xb6ffb43c - 0xb6ffc6b4 is .gnu.hash in ./libnx_ec_node_mgr.so
	0xb6ffc6b4 - 0xb6fff294 is .dynsym in ./libnx_ec_node_mgr.so
	0xb6fff294 - 0xb7005841 is .dynstr in ./libnx_ec_node_mgr.so
	0xb7005842 - 0xb7005dbe is .gnu.version in ./libnx_ec_node_mgr.so
	0xb7005dc0 - 0xb7005ee0 is .gnu.version_r in ./libnx_ec_node_mgr.so
	0xb7005ee0 - 0xb7009a50 is .rel.dyn in ./libnx_ec_node_mgr.so
	0xb7009a50 - 0xb700a778 is .rel.plt in ./libnx_ec_node_mgr.so
	0xb700a778 - 0xb700a7a8 is .init in ./libnx_ec_node_mgr.so
	0xb700a7a8 - 0xb700c208 is .plt in ./libnx_ec_node_mgr.so
	0xb700c210 - 0xb70403b8 is .text in ./libnx_ec_node_mgr.so
	0xb70403b8 - 0xb70403d4 is .fini in ./libnx_ec_node_mgr.so
	0xb70403e0 - 0xb7043df7 is .rodata in ./libnx_ec_node_mgr.so
	0xb7043df8 - 0xb7044df4 is .eh_frame_hdr in ./libnx_ec_node_mgr.so
	0xb7044df4 - 0xb70491b4 is .eh_frame in ./libnx_ec_node_mgr.so
	0xb70491b4 - 0xb704c29c is .gcc_except_table in ./libnx_ec_node_mgr.so
	0xb704d29c - 0xb704d2f4 is .ctors in ./libnx_ec_node_mgr.so
	0xb704d2f4 - 0xb704d2fc is .dtors in ./libnx_ec_node_mgr.so
	0xb704d2fc - 0xb704d300 is .jcr in ./libnx_ec_node_mgr.so
	0xb704d300 - 0xb704d488 is .data.rel.ro in ./libnx_ec_node_mgr.so
	0xb704d488 - 0xb704d580 is .dynamic in ./libnx_ec_node_mgr.so
	0xb704d580 - 0xb704d5f8 is .got in ./libnx_ec_node_mgr.so
	0xb704d5f8 - 0xb704dc98 is .got.plt in ./libnx_ec_node_mgr.so
	0xb704dc98 - 0xb704dca0 is .data in ./libnx_ec_node_mgr.so
	0xb704dca0 - 0xb704dd0c is .bss in ./libnx_ec_node_mgr.so
	0xb67740f4 - 0xb6774118 is .note.gnu.build-id in /opt/z2000/lib/libnx_mb.so
	0xb6774118 - 0xb67765a0 is .hash in /opt/z2000/lib/libnx_mb.so
	0xb67765a0 - 0xb677816c is .gnu.hash in /opt/z2000/lib/libnx_mb.so
	0xb677816c - 0xb677d2fc is .dynsym in /opt/z2000/lib/libnx_mb.so
	0xb677d2fc - 0xb6789b89 is .dynstr in /opt/z2000/lib/libnx_mb.so
	0xb6789b8a - 0xb678a5bc is .gnu.version in /opt/z2000/lib/libnx_mb.so
	0xb678a5bc - 0xb678a70c is .gnu.version_r in /opt/z2000/lib/libnx_mb.so
	0xb678a70c - 0xb678bb44 is .rel.dyn in /opt/z2000/lib/libnx_mb.so
	0xb678bb44 - 0xb678d3ac is .rel.plt in /opt/z2000/lib/libnx_mb.so
	0xb678d3ac - 0xb678d3dc is .init in /opt/z2000/lib/libnx_mb.so
	0xb678d3dc - 0xb67904bc is .plt in /opt/z2000/lib/libnx_mb.so
	0xb67904c0 - 0xb67e4478 is .text in /opt/z2000/lib/libnx_mb.so
	0xb67e4478 - 0xb67e4494 is .fini in /opt/z2000/lib/libnx_mb.so
	0xb67e44a0 - 0xb67e7e8c is .rodata in /opt/z2000/lib/libnx_mb.so
	0xb67e7e8c - 0xb67e99b8 is .eh_frame_hdr in /opt/z2000/lib/libnx_mb.so
	0xb67e99b8 - 0xb67f0b68 is .eh_frame in /opt/z2000/lib/libnx_mb.so
	0xb67f0b68 - 0xb67f6bcb is .gcc_except_table in /opt/z2000/lib/libnx_mb.so
	0xb67f7000 - 0xb67f708c is .ctors in /opt/z2000/lib/libnx_mb.so
	0xb67f708c - 0xb67f7094 is .dtors in /opt/z2000/lib/libnx_mb.so
	0xb67f7094 - 0xb67f7098 is .jcr in /opt/z2000/lib/libnx_mb.so
	0xb67f70a0 - 0xb67f7d90 is .data.rel.ro in /opt/z2000/lib/libnx_mb.so
	0xb67f7d90 - 0xb67f7e98 is .dynamic in /opt/z2000/lib/libnx_mb.so
	0xb67f7e98 - 0xb67f7ffc is .got in /opt/z2000/lib/libnx_mb.so
	0xb67f7ffc - 0xb67f8c3c is .got.plt in /opt/z2000/lib/libnx_mb.so
	0xb67f8c3c - 0xb67f8c60 is .data in /opt/z2000/lib/libnx_mb.so
	0xb67f8c60 - 0xb67f8dd0 is .bss in /opt/z2000/lib/libnx_mb.so
	0xb6746154 - 0xb6746178 is .note.gnu.build-id in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746178 - 0xb6746198 is .note.ABI-tag in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746198 - 0xb6746828 is .gnu.hash in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6746828 - 0xb6747788 is .dynsym in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6747788 - 0xb6748414 is .dynstr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748414 - 0xb6748600 is .gnu.version in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748600 - 0xb67486a4 is .gnu.version_d in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67486a4 - 0xb6748724 is .gnu.version_r in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748724 - 0xb67487bc is .rel.dyn in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67487bc - 0xb6748b04 is .rel.plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748b04 - 0xb6748b27 is .init in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6748b30 - 0xb67491d0 is .plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb67491d0 - 0xb6755723 is .text in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6755724 - 0xb6755738 is .fini in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6755740 - 0xb6757540 is .rodata in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757540 - 0xb6757553 is .interp in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757554 - 0xb6757b00 is .eh_frame_hdr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb6757b00 - 0xb675a3c0 is .eh_frame in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675a3c0 - 0xb675aeec is .hash in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beac - 0xb675beb0 is .init_array in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb0 - 0xb675beb4 is .fini_array in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb4 - 0xb675beb8 is .jcr in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675beb8 - 0xb675bfc0 is .dynamic in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675bfc0 - 0xb675c000 is .got in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c000 - 0xb675c1b0 is .got.plt in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c1b0 - 0xb675c1b4 is .data in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb675c1c0 - 0xb675e7e8 is .bss in /lib/i386-linux-gnu/i686/cmov/libnsl.so.1
	0xb64fd0f4 - 0xb64fd118 is .note.gnu.build-id in /opt/z2000/lib/libIceE.so.13
	0xb64fd118 - 0xb6508998 is .hash in /opt/z2000/lib/libIceE.so.13
	0xb6508998 - 0xb6515f08 is .gnu.hash in /opt/z2000/lib/libIceE.so.13
	0xb6515f08 - 0xb65340b8 is .dynsym in /opt/z2000/lib/libIceE.so.13
	0xb65340b8 - 0xb65c2a87 is .dynstr in /opt/z2000/lib/libIceE.so.13
	0xb65c2a88 - 0xb65c66be is .gnu.version in /opt/z2000/lib/libIceE.so.13
	0xb65c66c0 - 0xb65c67d0 is .gnu.version_r in /opt/z2000/lib/libIceE.so.13
	0xb65c67d0 - 0xb65ce040 is .rel.dyn in /opt/z2000/lib/libIceE.so.13
	0xb65ce040 - 0xb65d96e0 is .rel.plt in /opt/z2000/lib/libIceE.so.13
	0xb65d96e0 - 0xb65d9710 is .init in /opt/z2000/lib/libIceE.so.13
	0xb65d9710 - 0xb65f0460 is .plt in /opt/z2000/lib/libIceE.so.13
	0xb65f0460 - 0xb66d4b48 is .text in /opt/z2000/lib/libIceE.so.13
	0xb66d4b48 - 0xb66d4b64 is .fini in /opt/z2000/lib/libIceE.so.13
	0xb66d4b80 - 0xb66e4ce0 is .rodata in /opt/z2000/lib/libIceE.so.13
	0xb66e4ce0 - 0xb66f236c is .eh_frame_hdr in /opt/z2000/lib/libIceE.so.13
	0xb66f236c - 0xb67258d4 is .eh_frame in /opt/z2000/lib/libIceE.so.13
	0xb67258d4 - 0xb67397a1 is .gcc_except_table in /opt/z2000/lib/libIceE.so.13
	0xb673a000 - 0xb673a048 is .ctors in /opt/z2000/lib/libIceE.so.13
	0xb673a048 - 0xb673a050 is .dtors in /opt/z2000/lib/libIceE.so.13
	0xb673a050 - 0xb673a054 is .jcr in /opt/z2000/lib/libIceE.so.13
	0xb673a060 - 0xb673f7cc is .data.rel.ro in /opt/z2000/lib/libIceE.so.13
	0xb673f7cc - 0xb673f8d4 is .dynamic in /opt/z2000/lib/libIceE.so.13
	0xb673f8d4 - 0xb673fe78 is .got in /opt/z2000/lib/libIceE.so.13
	0xb673fe78 - 0xb67459d4 is .got.plt in /opt/z2000/lib/libIceE.so.13
	0xb67459d4 - 0xb6745b3c is .data in /opt/z2000/lib/libIceE.so.13
	0xb6745b40 - 0xb6745d24 is .bss in /opt/z2000/lib/libIceE.so.13
	0xae6f30f4 - 0xae6f3118 is .note.gnu.build-id in ./libnx_ec_pro_srv_gw104.so
	0xae6f3118 - 0xae6f5d18 is .gnu.hash in ./libnx_ec_pro_srv_gw104.so
	0xae6f5d18 - 0xae6fc878 is .dynsym in ./libnx_ec_pro_srv_gw104.so
	0xae6fc878 - 0xae7100c4 is .dynstr in ./libnx_ec_pro_srv_gw104.so
	0xae7100c4 - 0xae710e30 is .gnu.version in ./libnx_ec_pro_srv_gw104.so
	0xae710e30 - 0xae710f90 is .gnu.version_r in ./libnx_ec_pro_srv_gw104.so
	0xae710f90 - 0xae71a8c0 is .rel.dyn in ./libnx_ec_pro_srv_gw104.so
	0xae71a8c0 - 0xae71bb80 is .rel.plt in ./libnx_ec_pro_srv_gw104.so
	0xae71bb80 - 0xae71bba3 is .init in ./libnx_ec_pro_srv_gw104.so
	0xae71bbb0 - 0xae71e140 is .plt in ./libnx_ec_pro_srv_gw104.so
	0xae71e140 - 0xae7cffb4 is .text in ./libnx_ec_pro_srv_gw104.so
	0xae7cffb4 - 0xae7cffc8 is .fini in ./libnx_ec_pro_srv_gw104.so
	0xae7d0000 - 0xae7dbfe4 is .rodata in ./libnx_ec_pro_srv_gw104.so
	0xae7dbfe4 - 0xae7de8d0 is .eh_frame_hdr in ./libnx_ec_pro_srv_gw104.so
	0xae7de8d0 - 0xae7f2410 is .eh_frame in ./libnx_ec_pro_srv_gw104.so
	0xae7f2410 - 0xae7fa1c4 is .gcc_except_table in ./libnx_ec_pro_srv_gw104.so
	0xae7fb1c4 - 0xae7fb2b4 is .init_array in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2b4 - 0xae7fb2b8 is .fini_array in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2b8 - 0xae7fb2bc is .jcr in ./libnx_ec_pro_srv_gw104.so
	0xae7fb2c0 - 0xae7fdca8 is .data.rel.ro in ./libnx_ec_pro_srv_gw104.so
	0xae7fdca8 - 0xae7fddb8 is .dynamic in ./libnx_ec_pro_srv_gw104.so
	0xae7fddb8 - 0xae7fdec4 is .got in ./libnx_ec_pro_srv_gw104.so
	0xae7fdec4 - 0xae7fe830 is .got.plt in ./libnx_ec_pro_srv_gw104.so
	0xae7fe840 - 0xae7fe8e4 is .data in ./libnx_ec_pro_srv_gw104.so
	0xae7fe900 - 0xae7fed98 is .bss in ./libnx_ec_pro_srv_gw104.so
	0xb13b00f4 - 0xb13b0118 is .note.gnu.build-id in ./libnx_ec_pro_commuflow_104.so
	0xb13b0118 - 0xb13b129c is .hash in ./libnx_ec_pro_commuflow_104.so
	0xb13b129c - 0xb13b1f7c is .gnu.hash in ./libnx_ec_pro_commuflow_104.so
	0xb13b1f7c - 0xb13b44dc is .dynsym in ./libnx_ec_pro_commuflow_104.so
	0xb13b44dc - 0xb13b94da is .dynstr in ./libnx_ec_pro_commuflow_104.so
	0xb13b94da - 0xb13b9986 is .gnu.version in ./libnx_ec_pro_commuflow_104.so
	0xb13b9988 - 0xb13b9aa8 is .gnu.version_r in ./libnx_ec_pro_commuflow_104.so
	0xb13b9aa8 - 0xb13bc860 is .rel.dyn in ./libnx_ec_pro_commuflow_104.so
	0xb13bc860 - 0xb13bd210 is .rel.plt in ./libnx_ec_pro_commuflow_104.so
	0xb13bd210 - 0xb13bd240 is .init in ./libnx_ec_pro_commuflow_104.so
	0xb13bd240 - 0xb13be5b0 is .plt in ./libnx_ec_pro_commuflow_104.so
	0xb13be5b0 - 0xb13e6d88 is .text in ./libnx_ec_pro_commuflow_104.so
	0xb13e6d88 - 0xb13e6da4 is .fini in ./libnx_ec_pro_commuflow_104.so
	0xb13e6dc0 - 0xb13e98a3 is .rodata in ./libnx_ec_pro_commuflow_104.so
	0xb13e98a4 - 0xb13ea570 is .eh_frame_hdr in ./libnx_ec_pro_commuflow_104.so
	0xb13ea570 - 0xb13edb54 is .eh_frame in ./libnx_ec_pro_commuflow_104.so
	0xb13edb54 - 0xb13f0078 is .gcc_except_table in ./libnx_ec_pro_commuflow_104.so
	0xb13f1078 - 0xb13f10c4 is .ctors in ./libnx_ec_pro_commuflow_104.so
	0xb13f10c4 - 0xb13f10cc is .dtors in ./libnx_ec_pro_commuflow_104.so
	0xb13f10cc - 0xb13f10d0 is .jcr in ./libnx_ec_pro_commuflow_104.so
	0xb13f10e0 - 0xb13f1590 is .data.rel.ro in ./libnx_ec_pro_commuflow_104.so
	0xb13f1590 - 0xb13f1688 is .dynamic in ./libnx_ec_pro_commuflow_104.so
	0xb13f1688 - 0xb13f16f4 is .got in ./libnx_ec_pro_commuflow_104.so
	0xb13f16f4 - 0xb13f1bd8 is .got.plt in ./libnx_ec_pro_commuflow_104.so
	0xb13f1bd8 - 0xb13f1be4 is .data in ./libnx_ec_pro_commuflow_104.so
	0xb13f1be4 - 0xb13f1c30 is .bss in ./libnx_ec_pro_commuflow_104.so
	0xb135a0f4 - 0xb135a118 is .note.gnu.build-id in ./libnx_ec_msg_operation.so
	0xb135a118 - 0xb135b478 is .hash in ./libnx_ec_msg_operation.so
	0xb135b478 - 0xb135c72c is .gnu.hash in ./libnx_ec_msg_operation.so
	0xb135c72c - 0xb135f3fc is .dynsym in ./libnx_ec_msg_operation.so
	0xb135f3fc - 0xb1365b73 is .dynstr in ./libnx_ec_msg_operation.so
	0xb1365b74 - 0xb136610e is .gnu.version in ./libnx_ec_msg_operation.so
	0xb1366110 - 0xb1366230 is .gnu.version_r in ./libnx_ec_msg_operation.so
	0xb1366230 - 0xb1369a38 is .rel.dyn in ./libnx_ec_msg_operation.so
	0xb1369a38 - 0xb136a638 is .rel.plt in ./libnx_ec_msg_operation.so
	0xb136a638 - 0xb136a668 is .init in ./libnx_ec_msg_operation.so
	0xb136a668 - 0xb136be78 is .plt in ./libnx_ec_msg_operation.so
	0xb136be80 - 0xb13a2fc8 is .text in ./libnx_ec_msg_operation.so
	0xb13a2fc8 - 0xb13a2fe4 is .fini in ./libnx_ec_msg_operation.so
	0xb13a3000 - 0xb13a608f is .rodata in ./libnx_ec_msg_operation.so
	0xb13a6090 - 0xb13a7104 is .eh_frame_hdr in ./libnx_ec_msg_operation.so
	0xb13a7104 - 0xb13ab5c4 is .eh_frame in ./libnx_ec_msg_operation.so
	0xb13ab5c4 - 0xb13ae458 is .gcc_except_table in ./libnx_ec_msg_operation.so
	0xb13af458 - 0xb13af4b8 is .ctors in ./libnx_ec_msg_operation.so
	0xb13af4b8 - 0xb13af4c0 is .dtors in ./libnx_ec_msg_operation.so
	0xb13af4c0 - 0xb13af4c4 is .jcr in ./libnx_ec_msg_operation.so
	0xb13af4e0 - 0xb13af810 is .data.rel.ro in ./libnx_ec_msg_operation.so
	0xb13af810 - 0xb13af908 is .dynamic in ./libnx_ec_msg_operation.so
	0xb13af908 - 0xb13af970 is .got in ./libnx_ec_msg_operation.so
	0xb13af970 - 0xb13aff7c is .got.plt in ./libnx_ec_msg_operation.so
	0xb13aff7c - 0xb13aff84 is .data in ./libnx_ec_msg_operation.so
	0xb13aff84 - 0xb13affe4 is .bss in ./libnx_ec_msg_operation.so
	0xb13130f4 - 0xb1313118 is .note.gnu.build-id in ./libnx_ec_pro_operation.so
	0xb1313118 - 0xb13141f0 is .hash in ./libnx_ec_pro_operation.so
	0xb13141f0 - 0xb1314d10 is .gnu.hash in ./libnx_ec_pro_operation.so
	0xb1314d10 - 0xb1316fc0 is .dynsym in ./libnx_ec_pro_operation.so
	0xb1316fc0 - 0xb131bea8 is .dynstr in ./libnx_ec_pro_operation.so
	0xb131bea8 - 0xb131c2fe is .gnu.version in ./libnx_ec_pro_operation.so
	0xb131c300 - 0xb131c420 is .gnu.version_r in ./libnx_ec_pro_operation.so
	0xb131c420 - 0xb131e830 is .rel.dyn in ./libnx_ec_pro_operation.so
	0xb131e830 - 0xb131f2c8 is .rel.plt in ./libnx_ec_pro_operation.so
	0xb131f2c8 - 0xb131f2f8 is .init in ./libnx_ec_pro_operation.so
	0xb131f2f8 - 0xb1320838 is .plt in ./libnx_ec_pro_operation.so
	0xb1320840 - 0xb134f8e8 is .text in ./libnx_ec_pro_operation.so
	0xb134f8e8 - 0xb134f904 is .fini in ./libnx_ec_pro_operation.so
	0xb134f920 - 0xb13522ee is .rodata in ./libnx_ec_pro_operation.so
	0xb13522f0 - 0xb1352e9c is .eh_frame_hdr in ./libnx_ec_pro_operation.so
	0xb1352e9c - 0xb13560a4 is .eh_frame in ./libnx_ec_pro_operation.so
	0xb13560a4 - 0xb1358ba4 is .gcc_except_table in ./libnx_ec_pro_operation.so
	0xb1359000 - 0xb1359040 is .ctors in ./libnx_ec_pro_operation.so
	0xb1359040 - 0xb1359048 is .dtors in ./libnx_ec_pro_operation.so
	0xb1359048 - 0xb135904c is .jcr in ./libnx_ec_pro_operation.so
	0xb1359060 - 0xb1359308 is .data.rel.ro in ./libnx_ec_pro_operation.so
	0xb1359308 - 0xb1359400 is .dynamic in ./libnx_ec_pro_operation.so
	0xb1359400 - 0xb1359474 is .got in ./libnx_ec_pro_operation.so
	0xb1359474 - 0xb13599cc is .got.plt in ./libnx_ec_pro_operation.so
	0xb13599cc - 0xb13599d4 is .data in ./libnx_ec_pro_operation.so
	0xb13599d4 - 0xb1359a14 is .bss in ./libnx_ec_pro_operation.so

===============================================================================
崩溃信息
===============================================================================

--- 崩溃位置 ---
#0  0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#1  0xb1371e58 in CNXEcSrvMsgOperaObj::__OnEventRecv(void*, _NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#2  0xb138db2b in CNXObserver::PushEventNotify(_NX_EVENT_MESSAGE&, int) () from ./libnx_ec_msg_operation.so
#3  0xb70ff3bd in CNXEcSrvMediator::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) () from ./libnx_ec_srv_mediator.so
#4  0xb7101f9e in SendEventMsgToObserver () from ./libnx_ec_srv_mediator.so
#5  0x080660cb in CNXLoadSrvMedLib::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) ()
#6  0x08067813 in CNXSubject::SendEventNotify(_NX_EVENT_MESSAGE&) ()
#7  0xb70bdd54 in CNXEcBusSwap::__EventAllotLoop() () from ./libnx_ec_bus_swap.so
#8  0xb70bdfa3 in CNXEcBusSwap::__OnEventAllotThreadExec(void*, void*) () from ./libnx_ec_bus_swap.so
#9  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#10 0xb7710efb in start_thread (arg=0xaf9eeb40) at pthread_create.c:309
#11 0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

--- 详细调用栈 ---
#0  0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
No symbol table info available.
#1  0xb1371e58 in CNXEcSrvMsgOperaObj::__OnEventRecv(void*, _NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
No symbol table info available.
#2  0xb138db2b in CNXObserver::PushEventNotify(_NX_EVENT_MESSAGE&, int) () from ./libnx_ec_msg_operation.so
No symbol table info available.
#3  0xb70ff3bd in CNXEcSrvMediator::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) () from ./libnx_ec_srv_mediator.so
No symbol table info available.
#4  0xb7101f9e in SendEventMsgToObserver () from ./libnx_ec_srv_mediator.so
No symbol table info available.
#5  0x080660cb in CNXLoadSrvMedLib::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) ()
No symbol table info available.
#6  0x08067813 in CNXSubject::SendEventNotify(_NX_EVENT_MESSAGE&) ()
No symbol table info available.
#7  0xb70bdd54 in CNXEcBusSwap::__EventAllotLoop() () from ./libnx_ec_bus_swap.so
No symbol table info available.
#8  0xb70bdfa3 in CNXEcBusSwap::__OnEventAllotThreadExec(void*, void*) () from ./libnx_ec_bus_swap.so
No symbol table info available.
#9  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
No symbol table info available.
#10 0xb7710efb in start_thread (arg=0xaf9eeb40) at pthread_create.c:309
        __res = <optimized out>
        pd = 0xaf9eeb40
        now = <optimized out>
        unwind_buf = {
          cancel_jmp_buf =             {[0] = {
              jmp_buf =                 {[0] = -1217253376,
                [1] = -1348539584,
                [2] = 4001536,
                [3] = -1348541400,
                [4] = 373445707,
                [5] = -912710534}, 
              mask_was_saved = 0
            }}, 
          priv = {
            pad =               {[0] = 0x0,
              [1] = 0x0,
              [2] = 0x0,
              [3] = 0x0}, 
            data = {
              prev = 0x0, 
              cleanup = 0x0, 
              canceltype = 0
            }
          }
        }
        not_first_call = <optimized out>
        pagesize_m1 = <optimized out>
        sp = <optimized out>
        freesize = <optimized out>
        __PRETTY_FUNCTION__ =           "start_thread"
#11 0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129
No locals.

--- 调用栈（仅函数名）---
#0  0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#1  0xb1371e58 in CNXEcSrvMsgOperaObj::__OnEventRecv(void*, _NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#2  0xb138db2b in CNXObserver::PushEventNotify(_NX_EVENT_MESSAGE&, int) () from ./libnx_ec_msg_operation.so
#3  0xb70ff3bd in CNXEcSrvMediator::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) () from ./libnx_ec_srv_mediator.so
#4  0xb7101f9e in SendEventMsgToObserver () from ./libnx_ec_srv_mediator.so
#5  0x080660cb in CNXLoadSrvMedLib::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) ()
#6  0x08067813 in CNXSubject::SendEventNotify(_NX_EVENT_MESSAGE&) ()
#7  0xb70bdd54 in CNXEcBusSwap::__EventAllotLoop() () from ./libnx_ec_bus_swap.so
#8  0xb70bdfa3 in CNXEcBusSwap::__OnEventAllotThreadExec(void*, void*) () from ./libnx_ec_bus_swap.so
#9  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#10 0xb7710efb in start_thread (arg=0xaf9eeb40) at pthread_create.c:309
#11 0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

===============================================================================
线程信息
===============================================================================
  Id   Target Id         Frame 
  20   Thread 0xb33f5b40 (LWP 2285) 0xb773dd40 in __kernel_vsyscall ()
  19   Thread 0xb1bf2b40 (LWP 2291) 0xb773dd40 in __kernel_vsyscall ()
  18   Thread 0xb01efb40 (LWP 2294) 0xb773dd40 in __kernel_vsyscall ()
  17   Thread 0xb6ff9b40 (LWP 2278) 0xb773dd40 in __kernel_vsyscall ()
  16   Thread 0xb09f0b40 (LWP 2293) 0xb773dd40 in __kernel_vsyscall ()
  15   Thread 0xb23f3b40 (LWP 2290) 0xb773dd40 in __kernel_vsyscall ()
  14   Thread 0xb73f7700 (LWP 2275) 0xb773dd40 in __kernel_vsyscall ()
  13   Thread 0xb2bf4b40 (LWP 2289) 0xb773dd40 in __kernel_vsyscall ()
  12   Thread 0xb11f1b40 (LWP 2292) 0xb773dd40 in __kernel_vsyscall ()
  11   Thread 0xb4bf8b40 (LWP 2282) 0xb773dd40 in __kernel_vsyscall ()
  10   Thread 0xb43f7b40 (LWP 2283) 0xb773dd40 in __kernel_vsyscall ()
  9    Thread 0xb53f9b40 (LWP 2281) 0xb773dd40 in __kernel_vsyscall ()
  8    Thread 0xaa6feb40 (LWP 2389) 0xb773dd40 in __kernel_vsyscall ()
  7    Thread 0xb5bfab40 (LWP 2280) 0xb773dd40 in __kernel_vsyscall ()
  6    Thread 0xa66f6b40 (LWP 16633) 0xb773dd40 in __kernel_vsyscall ()
  5    Thread 0xb63fbb40 (LWP 2279) 0xb773dd40 in __kernel_vsyscall ()
  4    Thread 0xaaeffb40 (LWP 2388) 0xb773dd40 in __kernel_vsyscall ()
  3    Thread 0xab8ffb40 (LWP 2387) 0xb773dd40 in __kernel_vsyscall ()
  2    Thread 0xaefffb40 (LWP 2296) 0xb773dd40 in __kernel_vsyscall ()
* 1    Thread 0xaf9eeb40 (LWP 2295) 0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so

--- 当前线程详细信息 ---
[Current thread is 1 (Thread 0xaf9eeb40 (LWP 2295))]

--- 所有线程的调用栈 ---

Thread 20 (Thread 0xb33f5b40 (LWP 2285)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67b4178 in CMbNodeConnStateMngr::Run() () from /opt/z2000/lib/libnx_mb.so
#5  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#6  0xb7710efb in start_thread (arg=0xb33f5b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 19 (Thread 0xb1bf2b40 (LWP 2291)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67c3675 in CNodeManagerProxy::Run() () from /opt/z2000/lib/libnx_mb.so
#5  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#6  0xb7710efb in start_thread (arg=0xb1bf2b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 18 (Thread 0xb01efb40 (LWP 2294)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb679b9ef in CNxmbSubOjbMngr::Run() () from /opt/z2000/lib/libnx_mb.so
#4  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#5  0xb7710efb in start_thread (arg=0xb01efb40) at pthread_create.c:309
#6  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 17 (Thread 0xb6ff9b40 (LWP 2278)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb700e69e in CNXEcCommuNodeMgr::___HandleCommonMsgLoop() () from ./libnx_ec_node_mgr.so
#4  0xb700ece3 in CNXEcCommuNodeMgr::__OnCmdExecThread(void*, void*) () from ./libnx_ec_node_mgr.so
#5  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#6  0xb7710efb in start_thread (arg=0xb6ff9b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 16 (Thread 0xb09f0b40 (LWP 2293)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67af65d in CMessageBusManager::Run() () from /opt/z2000/lib/libnx_mb.so
#5  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#6  0xb7710efb in start_thread (arg=0xb09f0b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 15 (Thread 0xb23f3b40 (LWP 2290)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67c7744 in CNodeIceSwitch::Run() () from /opt/z2000/lib/libnx_mb.so
#5  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#6  0xb7710efb in start_thread (arg=0xb23f3b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 14 (Thread 0xb73f7700 (LWP 2275)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb1327c1e in CNXEcSrvProOperation::ReleaseSource() () from ./libnx_ec_pro_operation.so
#4  0xb1320a82 in TNXEcProOperation::StopProOperation() () from ./libnx_ec_pro_operation.so
#5  0xae7ae170 in TNXEcProtocol::StopProLib() () from ./libnx_ec_pro_srv_gw104.so
#6  0xb701ae15 in CNXEcNodeChannel::Stop() () from ./libnx_ec_node_mgr.so
#7  0xb7017b67 in CNXEcClientNode::Stop() () from ./libnx_ec_node_mgr.so
#8  0xb700cb61 in CNXEcCommuNodeMgr::_StopRClientNode() () from ./libnx_ec_node_mgr.so
#9  0xb700cca8 in CNXEcCommuNodeMgr::StopCommuMgr(_COMMU_NODE_RUN_WAY) () from ./libnx_ec_node_mgr.so
#10 0xb701c86d in StopCommuMgr () from ./libnx_ec_node_mgr.so
#11 0x0806546d in CNXLoadNodeMgrLib::StopCommuMgr(_COMMU_NODE_RUN_WAY) ()
#12 0x080548b4 in CNXMainController::__StopNodeManger() ()
#13 0x08054d5a in CNXMainController::_StopOperation() ()
#14 0x08055149 in CNXMainController::BegineRun() ()
#15 0x08053741 in main ()

Thread 13 (Thread 0xb2bf4b40 (LWP 2289)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67a6e30 in CMbThread::RunSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#5  0xb67da416 in CNodeIceSwitchConn::Run() () from /opt/z2000/lib/libnx_mb.so
#6  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#7  0xb7710efb in start_thread (arg=0xb2bf4b40) at pthread_create.c:309
#8  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 12 (Thread 0xb11f1b40 (LWP 2292)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb67a7861 in CMbGlobal::MbSleep(unsigned int) () from /opt/z2000/lib/libnx_mb.so
#4  0xb67b1355 in CMbProxyRevDataMngr::Run() () from /opt/z2000/lib/libnx_mb.so
#5  0xb67a6c9a in _on_mb_thread_proc () from /opt/z2000/lib/libnx_mb.so
#6  0xb7710efb in start_thread (arg=0xb11f1b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 11 (Thread 0xb4bf8b40 (LWP 2282)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74e5876 in epoll_wait () at ../sysdeps/unix/syscall-template.S:81
#2  0xb66afb06 in IceInternal::Selector<IceInternal::SocketReadyCallback>::select (this=0x878b73c) at ../IceE/Selector.h:256
#3  0xb66ade96 in IceInternal::SelectorThread::run (this=0x878b710) at SelectorThread.cpp:161
#4  0xb66aea4e in IceInternal::SelectorThread::HelperThread::run (this=0x878db38) at SelectorThread.cpp:287
#5  0xb66b9dac in startHook (arg=0x878db38) at Thread.cpp:390
#6  0xb7710efb in start_thread (arg=0xb4bf8b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 10 (Thread 0xb43f7b40 (LWP 2283)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb7714c4b in pthread_cond_wait@@GLIBC_2.3.2 () at ../nptl/sysdeps/unix/sysv/linux/i386/i686/../i486/pthread_cond_wait.S:187
#2  0xb6618048 in IceUtil::Cond::waitImpl<IceUtil::Mutex> (this=0x878d334, mutex=...) at ../../include/IceE/Cond.h:208
#3  0xb6615eb2 in IceUtil::Monitor<IceUtil::Mutex>::wait (this=0x878d334) at ../../include/IceE/Monitor.h:161
#4  0xb66bff7e in IceInternal::ThreadPool::run (this=0x878d328) at ThreadPool.cpp:642
#5  0xb66c06f2 in IceInternal::ThreadPool::EventHandlerThread::run (this=0x878de80) at ThreadPool.cpp:747
#6  0xb66b9dac in startHook (arg=0x878de80) at Thread.cpp:390
#7  0xb7710efb in start_thread (arg=0xb43f7b40) at pthread_create.c:309
#8  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 9 (Thread 0xb53f9b40 (LWP 2281)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74e5876 in epoll_wait () at ../sysdeps/unix/syscall-template.S:81
#2  0xb66c1aba in IceInternal::Selector<IceInternal::EventHandler>::select (this=0x878b46c) at ../IceE/Selector.h:256
#3  0xb66beb42 in IceInternal::ThreadPool::run (this=0x878b408) at ThreadPool.cpp:330
#4  0xb66c06f2 in IceInternal::ThreadPool::EventHandlerThread::run (this=0x878db00) at ThreadPool.cpp:747
#5  0xb66b9dac in startHook (arg=0x878db00) at Thread.cpp:390
#6  0xb7710efb in start_thread (arg=0xb53f9b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 8 (Thread 0xaa6feb40 (LWP 2389)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb1321e40 in CNXEcSrvProOperation::__DoCallResponseLoop() () from ./libnx_ec_pro_operation.so
#4  0xb1321123 in TNXEcProOperation::_OnCallResponseThreadExec(void*, void*) () from ./libnx_ec_pro_operation.so
#5  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#6  0xb7710efb in start_thread (arg=0xaa6feb40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 7 (Thread 0xb5bfab40 (LWP 2280)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb7714c4b in pthread_cond_wait@@GLIBC_2.3.2 () at ../nptl/sysdeps/unix/sysv/linux/i386/i686/../i486/pthread_cond_wait.S:187
#2  0xb6618048 in IceUtil::Cond::waitImpl<IceUtil::Mutex> (this=0x878b2b4, mutex=...) at ../../include/IceE/Cond.h:208
#3  0xb6615eb2 in IceUtil::Monitor<IceUtil::Mutex>::wait (this=0x878b2b4) at ../../include/IceE/Monitor.h:161
#4  0xb66c5d0e in IceUtil::Timer::run (this=0x878b2b0) at Timer.cpp:151
#5  0xb66b9dac in startHook (arg=0x878b348) at Thread.cpp:390
#6  0xb7710efb in start_thread (arg=0xb5bfab40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 6 (Thread 0xa66f6b40 (LWP 16633)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74e5876 in epoll_wait () at ../sysdeps/unix/syscall-template.S:81
#2  0xb66c1aba in IceInternal::Selector<IceInternal::EventHandler>::select (this=0x878d38c) at ../IceE/Selector.h:256
#3  0xb66beb42 in IceInternal::ThreadPool::run (this=0x878d328) at ThreadPool.cpp:330
#4  0xb66c06f2 in IceInternal::ThreadPool::EventHandlerThread::run (this=0xac3004c8) at ThreadPool.cpp:747
#5  0xb66b9dac in startHook (arg=0xac3004c8) at Thread.cpp:390
#6  0xb7710efb in start_thread (arg=0xa66f6b40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 5 (Thread 0xb63fbb40 (LWP 2279)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb7714c4b in pthread_cond_wait@@GLIBC_2.3.2 () at ../nptl/sysdeps/unix/sysv/linux/i386/i686/../i486/pthread_cond_wait.S:187
#2  0xb6618048 in IceUtil::Cond::waitImpl<IceUtil::Mutex> (this=0x878b1ac, mutex=...) at ../../include/IceE/Cond.h:208
#3  0xb6615eb2 in IceUtil::Monitor<IceUtil::Mutex>::wait (this=0x878b1ac) at ../../include/IceE/Monitor.h:161
#4  0xb6627052 in IceInternal::EndpointHostResolver::run (this=0x878b188) at Endpoint.cpp:108
#5  0xb66b9dac in startHook (arg=0x878b188) at Thread.cpp:390
#6  0xb7710efb in start_thread (arg=0xb63fbb40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 4 (Thread 0xaaeffb40 (LWP 2388)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb7716df5 in sem_wait@@GLIBC_2.1 () at ../nptl/sysdeps/unix/sysv/linux/i386/i686/../i486/sem_wait.S:79
#2  0x0807a15d in sy_unname_sem_wait ()
#3  0xb1322a41 in CNXEcSrvProOperation::__CallCmdLoop(_CALL_CMD_HANDLE_PARAM*) () from ./libnx_ec_pro_operation.so
#4  0xb1321f17 in CNXEcSrvProOperation::_OnCallCmdThreadExec(void*, void*) () from ./libnx_ec_pro_operation.so
#5  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#6  0xb7710efb in start_thread (arg=0xaaeffb40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 3 (Thread 0xab8ffb40 (LWP 2387)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb7716df5 in sem_wait@@GLIBC_2.1 () at ../nptl/sysdeps/unix/sysv/linux/i386/i686/../i486/sem_wait.S:79
#2  0x0807a15d in sy_unname_sem_wait ()
#3  0xb1322a41 in CNXEcSrvProOperation::__CallCmdLoop(_CALL_CMD_HANDLE_PARAM*) () from ./libnx_ec_pro_operation.so
#4  0xb1321f17 in CNXEcSrvProOperation::_OnCallCmdThreadExec(void*, void*) () from ./libnx_ec_pro_operation.so
#5  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#6  0xb7710efb in start_thread (arg=0xab8ffb40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 2 (Thread 0xaefffb40 (LWP 2296)):
#0  0xb773dd40 in __kernel_vsyscall ()
#1  0xb74dd431 in select () at ../sysdeps/unix/syscall-template.S:81
#2  0x08079fe0 in sy_sleep ()
#3  0xb70bfc72 in CNXEcBusSwap::__CmdHandleLoop() () from ./libnx_ec_bus_swap.so
#4  0xb70c0383 in CNXEcBusSwap::__OnCmdHandleThreadExec(void*, void*) () from ./libnx_ec_bus_swap.so
#5  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#6  0xb7710efb in start_thread (arg=0xaefffb40) at pthread_create.c:309
#7  0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

Thread 1 (Thread 0xaf9eeb40 (LWP 2295)):
#0  0xb137534f in CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#1  0xb1371e58 in CNXEcSrvMsgOperaObj::__OnEventRecv(void*, _NX_EVENT_MESSAGE&) () from ./libnx_ec_msg_operation.so
#2  0xb138db2b in CNXObserver::PushEventNotify(_NX_EVENT_MESSAGE&, int) () from ./libnx_ec_msg_operation.so
#3  0xb70ff3bd in CNXEcSrvMediator::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) () from ./libnx_ec_srv_mediator.so
#4  0xb7101f9e in SendEventMsgToObserver () from ./libnx_ec_srv_mediator.so
#5  0x080660cb in CNXLoadSrvMedLib::SendEventMsgToObserver(_NX_EVENT_MESSAGE&, std::string&, int) ()
#6  0x08067813 in CNXSubject::SendEventNotify(_NX_EVENT_MESSAGE&) ()
#7  0xb70bdd54 in CNXEcBusSwap::__EventAllotLoop() () from ./libnx_ec_bus_swap.so
#8  0xb70bdfa3 in CNXEcBusSwap::__OnEventAllotThreadExec(void*, void*) () from ./libnx_ec_bus_swap.so
#9  0x080600fb in CNXECObject::__ProxyThreadPro(void**) ()
#10 0xb7710efb in start_thread (arg=0xaf9eeb40) at pthread_create.c:309
#11 0xb74e4ede in clone () at ../sysdeps/unix/sysv/linux/i386/clone.S:129

===============================================================================
寄存器信息
===============================================================================
eax            0xffffffff	-1
ecx            0x0	0
edx            0x0	0
ebx            0xb13af970	-1321535120
esp            0xaf9ed620	0xaf9ed620
ebp            0xaf9ed848	0xaf9ed848
esi            0x87925e0	142157280
edi            0xaf9ed754	-1348544684
eip            0xb137534f	0xb137534f <CNXEcSrvMsgOperaObj::__SaveEventToDisk(_NX_EVENT_MESSAGE&)+111>
eflags         0x10246	[ PF ZF IF RF ]
cs             0x73	115
ss             0x7b	123
ds             0x7b	123
es             0x7b	123
fs             0x0	0
gs             0x33	51

--- 浮点寄存器 ---
  R7: Empty   0x3fff8000000000000000
  R6: Empty   0x00000000000000000000
  R5: Empty   0x00000000000000000000
  R4: Empty   0x00000000000000000000
  R3: Empty   0x00000000000000000000
  R2: Empty   0x00000000000000000000
  R1: Empty   0x00000000000000000000
=>R0: Empty   0x00000000000000000000

Status Word:         0x0000                                            
                       TOP: 0
Control Word:        0x037f   IM DM ZM OM UM PM
                       PC: Extended Precision (64-bits)
                       RC: Round to nearest
Tag Word:            0xffff
Instruction Pointer: 0x00:0xb66bc5e6
Operand Pointer:     0x00:0x0878d3f8
Opcode:              0xdd98

--- 向量寄存器 ---
xmm0           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x6e,
    [0x1] = 0x75,
    [0x2] = 0x6d,
    [0x3] = 0x3d,
    [0x4] = 0x31,
    [0x5] = 0x2c,
    [0x6] = 0x6e,
    [0x7] = 0x5f,
    [0x8] = 0x64,
    [0x9] = 0x61,
    [0xa] = 0x74,
    [0xb] = 0x61,
    [0xc] = 0x5f,
    [0xd] = 0x73,
    [0xe] = 0x72,
    [0xf] = 0x63}, 
  v8_int16 =     {[0x0] = 0x756e,
    [0x1] = 0x3d6d,
    [0x2] = 0x2c31,
    [0x3] = 0x5f6e,
    [0x4] = 0x6164,
    [0x5] = 0x6174,
    [0x6] = 0x735f,
    [0x7] = 0x6372}, 
  v4_int32 =     {[0x0] = 0x3d6d756e,
    [0x1] = 0x5f6e2c31,
    [0x2] = 0x61746164,
    [0x3] = 0x6372735f}, 
  v2_int64 =     {[0x0] = 0x5f6e2c313d6d756e,
    [0x1] = 0x6372735f61746164}, 
  uint128 = 0x6372735f617461645f6e2c313d6d756e
}
xmm1           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v2_double =     {[0x0] = 0x0,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x6e,
    [0x1] = 0x74,
    [0x2] = 0x5f,
    [0x3] = 0x6f,
    [0x4] = 0x62,
    [0x5] = 0x6a,
    [0x6] = 0x3d,
    [0x7] = 0x34,
    [0x8] = 0x32,
    [0x9] = 0x2c,
    [0xa] = 0x65,
    [0xb] = 0x76,
    [0xc] = 0x65,
    [0xd] = 0x6e,
    [0xe] = 0x74,
    [0xf] = 0x5f}, 
  v8_int16 =     {[0x0] = 0x746e,
    [0x1] = 0x6f5f,
    [0x2] = 0x6a62,
    [0x3] = 0x343d,
    [0x4] = 0x2c32,
    [0x5] = 0x7665,
    [0x6] = 0x6e65,
    [0x7] = 0x5f74}, 
  v4_int32 =     {[0x0] = 0x6f5f746e,
    [0x1] = 0x343d6a62,
    [0x2] = 0x76652c32,
    [0x3] = 0x5f746e65}, 
  v2_int64 =     {[0x0] = 0x343d6a626f5f746e,
    [0x1] = 0x5f746e6576652c32}, 
  uint128 = 0x5f746e6576652c32343d6a626f5f746e
}
xmm2           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x78,
    [0x1] = 0x5f,
    [0x2] = 0x65,
    [0x3] = 0x63,
    [0x4] = 0x5f,
    [0x5] = 0x6d,
    [0x6] = 0x73,
    [0x7] = 0x67,
    [0x8] = 0x5f,
    [0x9] = 0x6f,
    [0xa] = 0x70,
    [0xb] = 0x65,
    [0xc] = 0x72,
    [0xd] = 0x61,
    [0xe] = 0x74,
    [0xf] = 0x69}, 
  v8_int16 =     {[0x0] = 0x5f78,
    [0x1] = 0x6365,
    [0x2] = 0x6d5f,
    [0x3] = 0x6773,
    [0x4] = 0x6f5f,
    [0x5] = 0x6570,
    [0x6] = 0x6172,
    [0x7] = 0x6974}, 
  v4_int32 =     {[0x0] = 0x63655f78,
    [0x1] = 0x67736d5f,
    [0x2] = 0x65706f5f,
    [0x3] = 0x69746172}, 
  v2_int64 =     {[0x0] = 0x67736d5f63655f78,
    [0x1] = 0x6974617265706f5f}, 
  uint128 = 0x6974617265706f5f67736d5f63655f78
}
xmm3           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x324,
    [0x2] = 0x0,
    [0x3] = 0xdb}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x6cf8b574a4c4f4}, 
  v16_int8 =     {[0x0] = 0x6f,
    [0x1] = 0x6e,
    [0x2] = 0x2e,
    [0x3] = 0x73,
    [0x4] = 0x6f,
    [0x5] = 0x28,
    [0x6] = 0x49,
    [0x7] = 0x44,
    [0x8] = 0x3d,
    [0x9] = 0x31,
    [0xa] = 0x29,
    [0xb] = 0x5d,
    [0xc] = 0x2d,
    [0xd] = 0x3e,
    [0xe] = 0x5b,
    [0xf] = 0x43}, 
  v8_int16 =     {[0x0] = 0x6e6f,
    [0x1] = 0x732e,
    [0x2] = 0x286f,
    [0x3] = 0x4449,
    [0x4] = 0x313d,
    [0x5] = 0x5d29,
    [0x6] = 0x3e2d,
    [0x7] = 0x435b}, 
  v4_int32 =     {[0x0] = 0x732e6e6f,
    [0x1] = 0x4449286f,
    [0x2] = 0x5d29313d,
    [0x3] = 0x435b3e2d}, 
  v2_int64 =     {[0x0] = 0x4449286f732e6e6f,
    [0x1] = 0x435b3e2d5d29313d}, 
  uint128 = 0x435b3e2d5d29313d4449286f732e6e6f
}
xmm4           {
  v4_float =     {[0x0] = 0x414c0000,
    [0x1] = 0xe8000000,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x4c,
    [0x1] = 0x41,
    [0x2] = 0x53,
    [0x3] = 0x53,
    [0x4] = 0x3a,
    [0x5] = 0x43,
    [0x6] = 0x4e,
    [0x7] = 0x58,
    [0x8] = 0x4f,
    [0x9] = 0x62,
    [0xa] = 0x73,
    [0xb] = 0x65,
    [0xc] = 0x72,
    [0xd] = 0x76,
    [0xe] = 0x65,
    [0xf] = 0x72}, 
  v8_int16 =     {[0x0] = 0x414c,
    [0x1] = 0x5353,
    [0x2] = 0x433a,
    [0x3] = 0x584e,
    [0x4] = 0x624f,
    [0x5] = 0x6573,
    [0x6] = 0x7672,
    [0x7] = 0x7265}, 
  v4_int32 =     {[0x0] = 0x5353414c,
    [0x1] = 0x584e433a,
    [0x2] = 0x6573624f,
    [0x3] = 0x72657672}, 
  v2_int64 =     {[0x0] = 0x584e433a5353414c,
    [0x1] = 0x726576726573624f}, 
  uint128 = 0x726576726573624f584e433a5353414c
}
xmm5           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x3d1b9940,
    [0x3] = 0x0}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x5d,
    [0x1] = 0x20,
    [0x2] = 0x50,
    [0x3] = 0x75,
    [0x4] = 0x73,
    [0x5] = 0x68,
    [0x6] = 0x45,
    [0x7] = 0x76,
    [0x8] = 0x65,
    [0x9] = 0x6e,
    [0xa] = 0x74,
    [0xb] = 0x4e,
    [0xc] = 0x6f,
    [0xd] = 0x74,
    [0xe] = 0x69,
    [0xf] = 0x66}, 
  v8_int16 =     {[0x0] = 0x205d,
    [0x1] = 0x7550,
    [0x2] = 0x6873,
    [0x3] = 0x7645,
    [0x4] = 0x6e65,
    [0x5] = 0x4e74,
    [0x6] = 0x746f,
    [0x7] = 0x6669}, 
  v4_int32 =     {[0x0] = 0x7550205d,
    [0x1] = 0x76456873,
    [0x2] = 0x4e746e65,
    [0x3] = 0x6669746f}, 
  v2_int64 =     {[0x0] = 0x764568737550205d,
    [0x1] = 0x6669746f4e746e65}, 
  uint128 = 0x6669746f4e746e65764568737550205d
}
xmm6           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x88600000,
    [0x2] = 0x0,
    [0x3] = 0xa8dfd}, 
  v2_double =     {[0x0] = 0x8000000000000000,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x79,
    [0x1] = 0x28,
    [0x2] = 0x29,
    [0x3] = 0x3a,
    [0x4] = 0xbd,
    [0x5] = 0xd3,
    [0x6] = 0xca,
    [0x7] = 0xd5,
    [0x8] = 0xc4,
    [0x9] = 0xbf,
    [0xa] = 0xb1,
    [0xb] = 0xea,
    [0xc] = 0xd5,
    [0xd] = 0xdf,
    [0xe] = 0x28,
    [0xf] = 0x49}, 
  v8_int16 =     {[0x0] = 0x2879,
    [0x1] = 0x3a29,
    [0x2] = 0xd3bd,
    [0x3] = 0xd5ca,
    [0x4] = 0xbfc4,
    [0x5] = 0xeab1,
    [0x6] = 0xdfd5,
    [0x7] = 0x4928}, 
  v4_int32 =     {[0x0] = 0x3a292879,
    [0x1] = 0xd5cad3bd,
    [0x2] = 0xeab1bfc4,
    [0x3] = 0x4928dfd5}, 
  v2_int64 =     {[0x0] = 0xd5cad3bd3a292879,
    [0x1] = 0x4928dfd5eab1bfc4}, 
  uint128 = 0x4928dfd5eab1bfc4d5cad3bd3a292879
}
xmm7           {
  v4_float =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0xffffff9b,
    [0x3] = 0xfffffd4d}, 
  v2_double =     {[0x0] = 0x0,
    [0x1] = 0x8000000000000000}, 
  v16_int8 =     {[0x0] = 0x44,
    [0x1] = 0x3d,
    [0x2] = 0x32,
    [0x3] = 0x30,
    [0x4] = 0x30,
    [0x5] = 0x30,
    [0x6] = 0x30,
    [0x7] = 0x30,
    [0x8] = 0x32,
    [0x9] = 0x29,
    [0xa] = 0xca,
    [0xb] = 0xc2,
    [0xc] = 0xbc,
    [0xd] = 0xfe,
    [0xe] = 0x2c,
    [0xf] = 0xc4}, 
  v8_int16 =     {[0x0] = 0x3d44,
    [0x1] = 0x3032,
    [0x2] = 0x3030,
    [0x3] = 0x3030,
    [0x4] = 0x2932,
    [0x5] = 0xc2ca,
    [0x6] = 0xfebc,
    [0x7] = 0xc42c}, 
  v4_int32 =     {[0x0] = 0x30323d44,
    [0x1] = 0x30303030,
    [0x2] = 0xc2ca2932,
    [0x3] = 0xc42cfebc}, 
  v2_int64 =     {[0x0] = 0x3030303030323d44,
    [0x1] = 0xc42cfebcc2ca2932}, 
  uint128 = 0xc42cfebcc2ca29323030303030323d44
}
mxcsr          0x1f80	[ IM DM ZM OM UM PM ]
mm0            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm1            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm2            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm3            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm4            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm5            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm6            {
  uint64 = 0x0, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x0}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x0}
}
mm7            {
  uint64 = 0x8000000000000000, 
  v2_int32 =     {[0x0] = 0x0,
    [0x1] = 0x80000000}, 
  v4_int16 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x8000}, 
  v8_int8 =     {[0x0] = 0x0,
    [0x1] = 0x0,
    [0x2] = 0x0,
    [0x3] = 0x0,
    [0x4] = 0x0,
    [0x5] = 0x0,
    [0x6] = 0x0,
    [0x7] = 0x80}
}

===============================================================================
内存信息
===============================================================================

--- 内存映射 ---
Mapped address spaces:

	Start Addr   End Addr       Size     Offset objfile
	 0x8048000  0x8088000    0x40000        0x0 /opt/z2000/nx_bin/nx_ec/nx_ec_service-*******
	 0x8088000  0x8089000     0x1000    0x40000 /opt/z2000/nx_bin/nx_ec/nx_ec_service-*******
	0xae6f3000 0xae7fb000   0x108000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_srv_gw104.so-1.0.27
	0xae7fb000 0xae7ff000     0x4000   0x107000 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_srv_gw104.so-1.0.27
	0xb1313000 0xb1359000    0x46000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_operation.so-1.0.7
	0xb1359000 0xb135a000     0x1000    0x46000 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_operation.so-1.0.7
	0xb135a000 0xb13af000    0x55000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_msg_operation.so-*******
	0xb13af000 0xb13b0000     0x1000    0x54000 /opt/z2000/nx_bin/nx_ec/libnx_ec_msg_operation.so-*******
	0xb13b0000 0xb13f1000    0x41000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_commuflow_104.so-1.0.1
	0xb13f1000 0xb13f2000     0x1000    0x40000 /opt/z2000/nx_bin/nx_ec/libnx_ec_pro_commuflow_104.so-1.0.1
	0xb64fd000 0xb673a000   0x23d000        0x0 /opt/z2000/nx_lib/libIceE.so.1.3.0
	0xb673a000 0xb6746000     0xc000   0x23d000 /opt/z2000/nx_lib/libIceE.so.1.3.0
	0xb6746000 0xb675b000    0x15000        0x0 /lib/i386-linux-gnu/i686/cmov/libnsl-2.19.so
	0xb675b000 0xb675c000     0x1000    0x15000 /lib/i386-linux-gnu/i686/cmov/libnsl-2.19.so
	0xb675c000 0xb675d000     0x1000    0x16000 /lib/i386-linux-gnu/i686/cmov/libnsl-2.19.so
	0xb6774000 0xb67f7000    0x83000        0x0 /opt/z2000/nx_lib/libnx_mb.so-1.0.7.3
	0xb67f7000 0xb67f9000     0x2000    0x83000 /opt/z2000/nx_lib/libnx_mb.so-1.0.7.3
	0xb6ffa000 0xb704d000    0x53000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_node_mgr.so-1.0.3
	0xb704d000 0xb704e000     0x1000    0x52000 /opt/z2000/nx_bin/nx_ec/libnx_ec_node_mgr.so-1.0.3
	0xb7076000 0xb70ab000    0x35000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_net_listen.so-1.0.1
	0xb70ab000 0xb70ac000     0x1000    0x34000 /opt/z2000/nx_bin/nx_ec/libnx_ec_net_listen.so-1.0.1
	0xb70ac000 0xb70f2000    0x46000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_bus_swap.so-1.0.0
	0xb70f2000 0xb70f3000     0x1000    0x45000 /opt/z2000/nx_bin/nx_ec/libnx_ec_bus_swap.so-1.0.0
	0xb70f3000 0xb7127000    0x34000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_srv_mediator.so-1.0.0
	0xb7127000 0xb7128000     0x1000    0x33000 /opt/z2000/nx_bin/nx_ec/libnx_ec_srv_mediator.so-1.0.0
	0xb7128000 0xb72ee000   0x1c6000        0x0 /opt/z2000/nx_lib/libplm_dbm_mysql.so-1.0.6
	0xb72ee000 0xb7337000    0x49000   0x1c6000 /opt/z2000/nx_lib/libplm_dbm_mysql.so-1.0.6
	0xb7338000 0xb739d000    0x65000        0x0 /opt/z2000/nx_lib/libnx_dbm.so-2.0.1.2
	0xb739d000 0xb739f000     0x2000    0x64000 /opt/z2000/nx_lib/libnx_dbm.so-2.0.1.2
	0xb739f000 0xb73f5000    0x56000        0x0 /opt/z2000/nx_bin/nx_ec/libnx_ec_model_access.so-1.0.2
	0xb73f5000 0xb73f6000     0x1000    0x56000 /opt/z2000/nx_bin/nx_ec/libnx_ec_model_access.so-1.0.2
	0xb73f9000 0xb75a0000   0x1a7000        0x0 /lib/i386-linux-gnu/i686/cmov/libc-2.19.so
	0xb75a0000 0xb75a2000     0x2000   0x1a7000 /lib/i386-linux-gnu/i686/cmov/libc-2.19.so
	0xb75a2000 0xb75a3000     0x1000   0x1a9000 /lib/i386-linux-gnu/i686/cmov/libc-2.19.so
	0xb75a6000 0xb75c2000    0x1c000        0x0 /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c2000 0xb75c3000     0x1000    0x1b000 /lib/i386-linux-gnu/libgcc_s.so.1
	0xb75c3000 0xb7607000    0x44000        0x0 /lib/i386-linux-gnu/i686/cmov/libm-2.19.so
	0xb7607000 0xb7608000     0x1000    0x43000 /lib/i386-linux-gnu/i686/cmov/libm-2.19.so
	0xb7608000 0xb7609000     0x1000    0x44000 /lib/i386-linux-gnu/i686/cmov/libm-2.19.so
	0xb7609000 0xb76ef000    0xe6000        0x0 /usr/lib/i386-linux-gnu/libstdc++.so.6.0.20
	0xb76ef000 0xb76f3000     0x4000    0xe6000 /usr/lib/i386-linux-gnu/libstdc++.so.6.0.20
	0xb76f3000 0xb76f4000     0x1000    0xea000 /usr/lib/i386-linux-gnu/libstdc++.so.6.0.20
	0xb76fb000 0xb7702000     0x7000        0x0 /lib/i386-linux-gnu/i686/cmov/librt-2.19.so
	0xb7702000 0xb7703000     0x1000     0x6000 /lib/i386-linux-gnu/i686/cmov/librt-2.19.so
	0xb7703000 0xb7704000     0x1000     0x7000 /lib/i386-linux-gnu/i686/cmov/librt-2.19.so
	0xb7705000 0xb7708000     0x3000        0x0 /lib/i386-linux-gnu/i686/cmov/libdl-2.19.so
	0xb7708000 0xb7709000     0x1000     0x2000 /lib/i386-linux-gnu/i686/cmov/libdl-2.19.so
	0xb7709000 0xb770a000     0x1000     0x3000 /lib/i386-linux-gnu/i686/cmov/libdl-2.19.so
	0xb770a000 0xb7722000    0x18000        0x0 /lib/i386-linux-gnu/i686/cmov/libpthread-2.19.so
	0xb7722000 0xb7723000     0x1000    0x17000 /lib/i386-linux-gnu/i686/cmov/libpthread-2.19.so
	0xb7723000 0xb7724000     0x1000    0x18000 /lib/i386-linux-gnu/i686/cmov/libpthread-2.19.so
	0xb772a000 0xb7735000     0xb000        0x0 /lib/i386-linux-gnu/i686/cmov/libnss_files-2.19.so
	0xb7735000 0xb7736000     0x1000     0xa000 /lib/i386-linux-gnu/i686/cmov/libnss_files-2.19.so
	0xb7736000 0xb7737000     0x1000     0xb000 /lib/i386-linux-gnu/i686/cmov/libnss_files-2.19.so
	0xb7740000 0xb775f000    0x1f000        0x0 /lib/i386-linux-gnu/ld-2.19.so
	0xb775f000 0xb7760000     0x1000    0x1f000 /lib/i386-linux-gnu/ld-2.19.so

--- 内存统计 ---
/tmp/gdb_analysis_script_30018.gdb:57: Error in sourced command file:
unable to handle request

===============================================================================
                          分析完成信息
===============================================================================

分析完成时间: 2025-08-15 14:02:46
GDB退出代码: 0
报告文件大小: 139K

===============================================================================
                          报告结束
===============================================================================
