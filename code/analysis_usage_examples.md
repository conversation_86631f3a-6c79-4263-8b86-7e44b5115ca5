# Core文件分析脚本使用指南

## 概述

`analysis.sh` 是一个功能强大的自动化 core 文件分析脚本，使用 GDB 批处理模式对程序崩溃进行深入分析，并生成结构化的调试报告。

## 主要功能

1. **自动化GDB分析**：执行全面的调试命令集合
2. **结构化输出**：生成易读的分析报告
3. **灵活配置**：支持命令行参数和脚本内配置
4. **错误处理**：完善的参数验证和错误提示
5. **详细日志**：可选的详细输出模式

## 快速开始

### 1. 基本用法

```bash
# 最简单的用法
./analysis.sh -e ./my_program -c ./core.12345

# 指定输出文件
./analysis.sh -e ./my_program -c ./core.12345 -o ./crash_report.txt
```

### 2. 完整参数示例

```bash
./analysis.sh \
    --executable /path/to/your/program \
    --core /path/to/core.file \
    --workdir /working/directory \
    --output /output/analysis_report.txt \
    --verbose \
    --save-script
```

### 3. 脚本内配置方式

如果你经常分析同一个程序，可以直接修改脚本顶部的配置区域：

```bash
# 编辑脚本配置
vim analysis.sh

# 修改以下变量
EXECUTABLE_PATH="/path/to/your/program"
CORE_FILE_PATH="/path/to/core.file"
WORK_DIR="/your/work/directory"
OUTPUT_REPORT="/path/to/report.txt"

# 然后直接运行
./analysis.sh
```

## 命令行参数详解

| 参数 | 长参数 | 说明 | 必需 |
|------|--------|------|------|
| `-e` | `--executable` | 可执行文件路径 | 是 |
| `-c` | `--core` | Core文件路径 | 是 |
| `-w` | `--workdir` | 工作目录路径 | 否 |
| `-o` | `--output` | 输出报告文件路径 | 否 |
| `-g` | `--gdb` | GDB可执行文件路径 | 否 |
| `-v` | `--verbose` | 启用详细输出模式 | 否 |
| `-q` | `--quiet` | 静默模式 | 否 |
| `-s` | `--save-script` | 保存GDB命令脚本 | 否 |
| `-h` | `--help` | 显示帮助信息 | 否 |

## 分析报告内容

生成的报告包含以下主要部分：

1. **基本信息**
   - 系统信息、时间戳
   - 文件信息和类型

2. **崩溃信息**
   - 崩溃位置和原因
   - 详细调用栈
   - 函数调用链

3. **线程信息**
   - 所有线程状态
   - 各线程调用栈

4. **寄存器信息**
   - 通用寄存器
   - 浮点寄存器
   - 向量寄存器

5. **内存信息**
   - 内存映射
   - 进程内存统计

6. **共享库信息**
   - 加载的动态库
   - 符号表信息

7. **变量信息**
   - 局部变量
   - 函数参数
   - 全局变量

8. **源代码信息**
   - 崩溃点源代码
   - 反汇编代码

## 实际使用场景

### 场景1：分析nx_ec_service崩溃

```bash
# 假设nx_ec_service程序崩溃产生了core文件
./analysis.sh \
    -e ./nx_ec_service/nx_ec_service \
    -c ./core.nx_ec_service.12345 \
    -o ./crash_reports/nx_ec_service_crash_$(date +%Y%m%d).txt \
    -v
```

### 场景2：批量分析多个core文件

```bash
#!/bin/bash
# 批量分析脚本示例

for core_file in ./cores/core.*; do
    if [[ -f "$core_file" ]]; then
        echo "分析 $core_file"
        ./analysis.sh \
            -e ./my_program \
            -c "$core_file" \
            -o "./reports/analysis_$(basename $core_file).txt" \
            -q
    fi
done
```

### 场景3：远程调试

```bash
# 在远程服务器上分析
ssh user@remote-server "cd /path/to/project && ./analysis.sh -e ./program -c ./core.file"

# 或者将报告传回本地
./analysis.sh -e ./program -c ./core.file -o ./report.txt
scp ./report.txt local-machine:/local/path/
```

## 故障排除

### 常见问题

1. **GDB未找到**
   ```bash
   # 安装GDB
   sudo apt-get install gdb  # Ubuntu/Debian
   sudo yum install gdb      # CentOS/RHEL
   
   # 或指定GDB路径
   ./analysis.sh -g /usr/local/bin/gdb -e program -c core
   ```

2. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x analysis.sh
   
   # 确保core文件可读
   chmod 644 core.file
   ```

3. **符号信息缺失**
   ```bash
   # 确保程序编译时包含调试信息
   gcc -g -o program source.c
   
   # 或安装调试符号包
   sudo apt-get install program-dbg
   ```

### 调试脚本本身

```bash
# 启用bash调试模式
bash -x ./analysis.sh -e program -c core

# 查看详细输出
./analysis.sh -e program -c core -v

# 保存GDB脚本以便手动调试
./analysis.sh -e program -c core -s
# 然后手动运行：gdb --command=/tmp/gdb_analysis_script_*.gdb program core
```

## 高级用法

### 自定义GDB命令

如果需要添加特定的分析命令，可以修改脚本中的 `create_gdb_script()` 函数，添加你需要的GDB命令。

### 集成到CI/CD

```yaml
# GitHub Actions示例
- name: Analyze core dump
  if: failure()
  run: |
    if ls core.* 1> /dev/null 2>&1; then
      ./analysis.sh -e ./my_program -c core.* -o crash_report.txt
      cat crash_report.txt
    fi
```

## 注意事项

1. **安全性**：core文件可能包含敏感信息，注意保护
2. **存储空间**：分析报告可能很大，注意磁盘空间
3. **符号信息**：确保有调试符号以获得最佳分析效果
4. **系统兼容性**：脚本在Linux系统上测试，其他系统可能需要调整

## 相关文档

- [GDB调试指南](./doc/gdb.md)
- [Core文件生成配置](./doc/core_dump_setup.md)
- [调试最佳实践](./doc/debugging_best_practices.md)
